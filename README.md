# Helper Components Documentation

这是 WeGame Helper Components 组件库的官方文档站点，基于 VitePress 构建。

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 本地开发

```bash
npm run dev
```

访问 http://localhost:5173

### 构建部署

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 📁 项目结构

```
helper-docs/
├── .vitepress/                 # VitePress 配置
│   ├── config.ts              # 站点配置
│   ├── theme/                 # 主题配置
│   │   ├── index.ts           # 主题入口
│   │   └── custom.css         # 自定义样式
│   ├── components/            # 全局组件
│   │   └── DemoContainer.vue  # Demo 容器组件
│   └── demos/                 # 组件 Demo
├── components/                # 组件文档
│   ├── carousel/             # 轮播图组件文档
│   ├── audio-player/         # 音频播放器文档
│   └── ...                   # 其他组件文档
├── guide/                    # 指南文档
│   ├── getting-started.md    # 快速开始
│   ├── installation.md      # 安装指南
│   └── development.md       # 开发指南
├── scripts/                  # 自动化脚本
│   ├── generate-component-docs.js  # 生成组件文档
│   ├── update-component-docs.js    # 更新组件文档
│   └── analyze-all-components.js   # 分析所有组件
├── index.md                  # 首页
├── package.json              # 项目配置
└── README.md                 # 项目说明
```

## 🤖 自动化功能

### 自动生成文档

当在主仓库中新增或修改组件时，GitHub Actions 会自动：

1. 检测组件变化
2. 分析组件源码
3. 生成/更新组件文档
4. 构建并部署到 GitHub Pages

### 手动生成文档

```bash
# 生成所有组件文档
npm run analyze-components

# 生成单个组件文档
npm run generate-docs carousel

# 更新单个组件文档
npm run update-docs carousel
```

## 📝 文档编写

### 组件文档结构

每个组件的文档包含：

- **组件描述**: 功能和特性说明
- **基础用法**: 最简单的使用示例
- **API 文档**: Props、Events、Slots 详细说明
- **使用示例**: 各种场景的代码示例
- **注意事项**: 使用时需要注意的问题

### Demo 组件

每个组件都有对应的 Demo 组件，位于 `demo/` 目录：

```vue
<!-- demo/carousel/basic.vue -->
<template>
  <DemoContainer>
    <template #demo>
      <Carousel :appId="appId" />
    </template>
    <template #code>
      <!-- 代码示例 -->
    </template>
  </DemoContainer>
</template>
```

### 文档自动生成规则

1. **Props 解析**: 从 TypeScript 接口自动提取
2. **Events 解析**: 从 `defineEmits` 自动提取
3. **描述信息**: 从 README.md 和注释中提取
4. **示例代码**: 自动生成基础用法示例

## 🔧 配置说明

### VitePress 配置

主要配置在 `.vitepress/config.ts`:

- 站点基本信息
- 导航菜单配置
- 侧边栏配置
- 主题配置
- 插件配置

### 组件别名

```typescript
// vite 配置中的别名
alias: {
  '@': resolve(__dirname, '../'),
  'helper-components': resolve(__dirname, '../../helper-components')
}
```

### 自定义样式

在 `.vitepress/theme/custom.css` 中自定义样式：

- 主题色配置
- Demo 容器样式
- 组件特定样式

## 🚀 部署

### GitHub Pages (自动)

当代码推送到 master 分支时，GitHub Actions 会自动构建并部署到 GitHub Pages。

### 手动部署

```bash
# 构建
npm run build

# 部署到 GitHub Pages
npm run deploy
```

### 其他平台部署

构建产物在 `.vitepress/dist` 目录，可以部署到任何静态文件服务器。

## 🛠️ 开发指南

### 添加新组件文档

1. **自动生成** (推荐):
   ```bash
   npm run generate-docs your-component-name
   ```

2. **手动创建**:
   - 在 `components/` 下创建组件目录
   - 创建 `index.md` 文档文件
   - 在 `demo/` 下创建 Demo 组件
   - 更新 `.vitepress/config.ts` 中的侧边栏配置

### 更新现有文档

```bash
npm run update-docs component-name
```

### 本地调试

1. 启动开发服务器: `npm run dev`
2. 修改文档内容，热重载会自动更新
3. 添加 Demo 组件，在文档中引用

### 代码规范

- 文档使用 Markdown 格式
- Vue 组件使用 `<script setup>` 语法
- 样式使用 CSS 变量以支持主题切换
- 代码示例需要完整可运行

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支: `git checkout -b feature/new-docs`
3. 提交更改: `git commit -am 'Add new docs'`
4. 推送分支: `git push origin feature/new-docs`
5. 提交 Pull Request

### 文档质量要求

- 内容准确完整
- 示例代码可运行
- 格式规范统一
- 中英文混排时注意空格

## 📚 相关资源

- [VitePress 官方文档](https://vitepress.dev/)
- [Vue 3 文档](https://vuejs.org/)
- [Helper Components 源码](https://git.woa.com/wegame/helper/helper-components)

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

> 如有问题或建议，请提交 Issue 或联系 WeGame 前端团队。 