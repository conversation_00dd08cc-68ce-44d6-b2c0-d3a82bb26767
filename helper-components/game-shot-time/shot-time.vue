<template>
  <div
    v-if="enable && white"
    class="
      tools-button tools-button--moment
      text-hover-tips text-hover-tips--lc
    "
    data-name="精彩时刻"
  >
    <div
      class="tools-button-icon"
      :class="iconClass"
      @click="openShotTime"
    ></div>

    <slot></slot>
    <ProgressFrame
      :progress-frame-visible="progressFrameVisible"
      :progress="downloadProgress"
      :color="pColor"
      :linear-start-color="pLinearStartColor"
      :linear-end-color="pLinearEndColor"
      @close="progressFrameVisibleFn(false)"
    />
    <GeneralDialog
      :title="failTitle"
      :general-text="failTips"
      :btn-text="'知道了'"
      :general-dialog-visible="flag"
      @close="flag = false"
      @confirm="flag = false"
    >
    </GeneralDialog>
  </div>
</template>
<script setup lang="ts">
import ProgressFrame from './progress-frame.vue';
import GeneralDialog from './general-dialog.vue';
import { useGameState } from '../composable/use-game-state';
import { onMounted, onUnmounted, ref, toRef, watch } from 'vue';
import {
  checkShowShotTime,
  launchShotTime,
  startListenShotTime,
  unlistenShotTime,
  ICreateStatus,
} from './shot-time';
import { fetchOssWhite, queryGaryInfo } from './oss-white';
import { cookie, isTourist, isWeGame } from '@tencent/wegame-web-sdk';
import { reportEvent } from '../utils/report';
const props = withDefaults(
  defineProps<{
    gameId: string;
    pColor?: string;
    pLinearStartColor?: string;
    pLinearEndColor?: string;
    iconClass?: string;
    needCheckWhite?: boolean;
  }>(),
  { iconClass: '', needCheckWhite: true }
);

// 显示进度弹层
const enable = ref(false);
const white = ref(false);
const flag = ref(false);
const progressFrameVisible = ref(false);
const downloadProgress = ref(0);
const failTitle = ref('');
const failTips = ref('');
function progressFrameVisibleFn(bool: boolean) {
  progressFrameVisible.value = bool;
}

const gameId = toRef(() => props.gameId);
const { gameState } = useGameState(gameId);

async function checkWhite(gameId: string) {
  if (props.needCheckWhite === false) {
    white.value = true;
    return;
  }
  if (!isWeGame) {
    white.value = false;
    return;
  }
  if (isTourist()) {
    white.value = false;
    return;
  }
  const [ossEnable, railEnable] = await Promise.all([
    checkOssWhite(gameId),
    checkGaryList(gameId),
  ]);
  white.value = ossEnable || railEnable;
}

// oss灰度尾号
async function checkOssWhite(gameId: string) {
  const res = await fetchOssWhite(gameId);
  // 找到配置+开启+名单命中展示
  if (res?.enable === 1 && res?.list.length) {
    const userId = cookie.get('tgp_id') || '';
    // 用尾号来判断
    const checked = res.list.find((code) => {
      return userId.match(new RegExp(`${code}$`));
    });
    if (checked) {
      return true;
    }
  }
  // 没找到配置 展示
  if (res?.enable === 0) {
    return false;
  }
  return false;
}

// 运营后台配置灰度名单
async function checkGaryList(gameId: string) {
  const res = await queryGaryInfo(gameId);
  return res;
}

watch(
  () => gameId.value,
  (val) => {
    if (val) {
      checkEnable();
    }
  }
);

watch(
  () => gameState.value,
  () => {
    checkEnable();
  }
);

onMounted(() => {
  // 监听下载组件回调
  startListenShotTime(resultCb, progressCb);
  if (gameId.value) {
    checkEnable();
  }
});

onUnmounted(() => {
  unlistenShotTime(resultCb, progressCb);
});

const resultCb = (data: { game_id: number; status: ICreateStatus }) => {
  if (String(data.game_id) === gameId.value) {
    if (data.status === ICreateStatus.success) {
      // 成功，关闭弹窗
      flag.value = false;
      progressFrameVisible.value = false;
    }
    if (data.status === ICreateStatus.update) {
      // 需要下载更新，弹出loading
      progressFrameVisible.value = true;
    }
    if (data.status === ICreateStatus.fail) {
      progressFrameVisible.value = false;
      flag.value = true;
      failTitle.value = '提示';
      failTips.value = '下载录屏组件失败';
    }
    if (data.status === ICreateStatus.launchFail) {
      progressFrameVisible.value = false;
      flag.value = true;
      failTitle.value = '提示';
      failTips.value = '加载录屏组件失败';
    }
  }
};
const progressCb = (data: { game_id: number; progress: number }) => {
  // progress 数值0-100；用于标识下载进度
  if (String(data.game_id) === gameId.value) {
    downloadProgress.value = data.progress;
  }
};

let checking = false;
async function checkEnable() {
  if (checking) {
    return;
  }
  checking = true;
  // 先查白名单
  await checkWhite(gameId.value);
  enable.value = await checkShowShotTime(gameId.value);
  checking = false;
}

const emits = defineEmits<(e: 'open') => void>();

function openShotTime() {
  if (!gameId.value) return;
  launchShotTime(gameId.value);
  emits('open');
  reportEvent({
    appId: gameId.value,
    block: 'shot_time_btn',
    action: 'click',
  });
}
</script>
