<template>
  <transition name="general-dialog">
    <div v-if="generalDialogVisible" class="general-dialog-box">
      <div class="general-dialog">
        <span class="general-dialog-close-btn" @click="closePopup"></span>
        <h3 class="general-dialog-tit">{{ title || '提示' }}</h3>
        <div class="general-dialog-content">
          <div class="general-dialog-txt">
            <span>{{ generalText }}</span>
            <slot></slot>
          </div>
          <div class="general-dialog-btn" @click="clickConfirm">{{
            btnText || '确认'
          }}</div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts" setup>
import './assets/scss/general-dialog.scss';
defineProps<{
  btnText?: string;
  title?: string;
  generalText: string;
  generalDialogVisible: Boolean;
}>();

const emits = defineEmits(['close', 'confirm']); // 定义一个或多个自定义事件

// 触发emits事件
const closePopup = () => {
  emits('close', false); // 第一个参数为自定义事件名  第二个参数为要传递的数据
};
function clickConfirm() {
  emits('confirm');
}
</script>
