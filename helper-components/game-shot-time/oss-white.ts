// 功能灰度，oss配置尾号白名单
import { baseRequest, cookie, isTourist } from '@tencent/wegame-web-sdk';

export interface WhiteConfig {
  game_id: string;
  white_numbers: string; // 尾号名单
  enable: number; // 0 - 不开启灰度，1 - 开启灰度
}
// 开播按钮是否展示
export async function fetchOssWhite(gameId: string) {
  try {
    const data = await baseRequest<{ items: WhiteConfig[] }>({
      url: '/api/rail/web/data_filter/game_config/query',
      method: 'POST',
      data: {
        data_names: 'helper_components_shot_time_white',
        command: 'list_all',
        params: { start_page: 0, items_per_pager: 50, filters: [] }
      }
    });
    if (data?.items) {
      const item = data.items.find(item => item.game_id === gameId);
      if (item) {
        return {
          ...item,
          enable: Number(item.enable),
          list: item.white_numbers.split(',')
        };
      }
      return {
        game_id: gameId,
        enable: 0,
        list: ''.split(',')
      };
    }
    return null;
  } catch (error) {
    console.error('fetch shot time white failed', error);
  }
  return null;
}

export async function queryGaryInfo(gameId: string | number) {
  if (isTourist()) {
    return false;
  }
  try {
    const resp = await baseRequest<{
      game_type_gray_info: [{ is_gray: number }];
    }>({
      url: '/api/rail/rpc/rail.GameGrayServer.CheckGrayInfo',
      method: 'POST',
      data: {
        tgpid: Number(cookie.get('tgp_id')),
        game_type_info: [{ gameid: Number(gameId), type: 4002 }] // 4002-运营后台配置“录屏组件”
      }
    });
    return resp?.game_type_gray_info[0]?.is_gray === 1 || false;
  } catch (error) {
    console.error('fetch oss gray failed', error);
  }
  return false;
}
