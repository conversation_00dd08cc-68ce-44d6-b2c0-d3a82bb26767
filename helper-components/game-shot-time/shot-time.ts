import { service } from '@tencent/wegame-web-sdk';

// 客户端查询是否支持游戏展示精彩时刻功能
export async function checkShowShotTime(gameId: string) {
  const res = await service.call<{ enable?: boolean } | undefined>(
    'Srv_ICreate_IsEnable',
    {
      game_id: gameId
    }
  );
  return res?.enable ?? false;
}

export enum ICreateStatus {
  success = 0, // 拉起组件成功
  update = 1, // 需要下载和更新，此时客户端会自动执行下载更新
  fail = 2, // 下载更新失败
  launchFail = 3 // 拉起失败
}

// 监听下载组件回调
export function startListenShotTime(resultCb: Function, progressCb: Function) {
  service.listen('Msg_ICreate_LaunchResult', resultCb as () => void);
  service.listen('Msg_ICreate_UpdateProgress', progressCb as () => void);
}

export function unlistenShotTime(resultCb: Function, progressCb: Function) {
  service.unlisten('Msg_ICreate_LaunchResult', resultCb as () => void);
  service.unlisten('Msg_ICreate_UpdateProgress', progressCb as () => void);
}

// 拉起组件
export function launchShotTime(gameId: string) {
  service.broadcast('Msg_ICreate_Launch', { game_id: Number(gameId) });
}