// helper-loading
:root{
  --helper-loading-width: 24px;
  --helper-loading-height: 24px;
  --helper-loading-line-width: 4px;
  --helper-loading-line-color: rgba(0, 0, 0, 0.1);
  --helper-loading-line-color-current: #4F6B87;
  --helper-loading-line-color-text: #4F6B87;
  --helper-loading-line-duration: 1s;
}
.helper-loading{
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  &-inner {
    color: var(--helper-loading-line-color-text);
    font-size: 12px;
    &::before{
      display: block;
      margin: 0 auto 3px;
      content: '';
      width: var(--helper-loading-width);
      height: var(--helper-loading-height);
      border: var(--helper-loading-line-width) solid var(--helper-loading-line-color);
      border-top-color: var(--helper-loading-line-color-current);
      animation: helpSpin var(--helper-loading-line-duration) infinite linear;
      border-radius: 50%;
    }
  }
}

@keyframes helpSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
