import { onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import { createSharedComposable } from '@vueuse/core';
import jsCookie from 'js-cookie';
import { getLocalStorageItem, setLocalStorageItem } from '../utils/storage';
import { AudioPlayer, PlayModeKey } from '../audio-player/audio-player';
import type { AudioItem } from '../audio-player/interface';

export const useAudio = createSharedComposable(
  (gameId = '', localKeyExt = '') => {
    /**
     * bg音频播放器
     */
    let bgPlayer: AudioPlayer | null = null;

    let bgAudioURL: string;
    const isPlayingBgAudio = ref(false);

    const tgpId = jsCookie.get('tgp_id');
    const audioMutedLocalKey = `helper-${gameId}-audio-muted-${tgpId}${localKeyExt}`;
    /**
     * LocalStorage 当前播放音乐
     */
    const audioLocalKey = `helper-audio-${gameId}-${tgpId}${localKeyExt}`;

    const isAudioMutedLoaded = ref(false);
    const isAudioMuted = ref(false);

    const curAudioURL = ref('');
    const curPlayMode = ref(PlayModeKey.SingleTrackRepeat);
    const audioList = ref<AudioItem[]>([]);

    /**
     * 其他音频播放中（包括视频）,例如视频列表，设置了则背景音乐不播放
     */
    const isOtherAudioPlaying = ref(false);

    const setOtherAudioPlaying = (val: boolean) => {
      isOtherAudioPlaying.value = val;
    };

    const handlePlayBgAudio = () => {
      isPlayingBgAudio.value = bgPlayer!.getIsPlaying();
    };

    const handlePauseBgAudio = () => {
      isPlayingBgAudio.value = bgPlayer!.getIsPlaying();
    };

    const handleEndBgAudio = () => {
      isPlayingBgAudio.value = bgPlayer!.getIsPlaying();
      // 循环播放
      if (
        curPlayMode.value === PlayModeKey.RandomPlayback &&
        audioList.value.length
      ) {
        const rnd = Math.floor(Math.random() * audioList.value.length);
        playBgAudio(audioList.value[rnd].url);
      } else {
        bgPlayer!.setCurrentTime(0);
        bgPlayer!.play();
      }
    };

    const isError = ref(false);

    const handleErrorBgAudio = () => {
      isError.value = true;
    };

    watch(
      () => isPlayingBgAudio.value,
      val => {
        if (val) {
          isError.value = false;
        }
      }
    );

    watch(
      () => isAudioMuted.value,
      () => {
        setLocalStorageItem(audioMutedLocalKey, isAudioMuted.value ? '1' : '0');
      }
    );

    const curAudioProgress = reactive<{
      currentTime: number;
      duration: number;
      progress: number;
    }>({
      currentTime: 0,
      duration: 0,
      progress: 0
    });

    const handleProgressBgAudio = (evt: CustomEvent) => {
      curAudioProgress.currentTime = evt.detail.currentTime;
      curAudioProgress.duration = evt.detail.duration;
      curAudioProgress.progress = evt.detail.progress;
    };

    const setBgAudioURL = (url: string) => {
      bgAudioURL = url;
      curAudioURL.value = bgAudioURL;
      setLocalStorageItem(audioLocalKey, bgAudioURL);
    };

    const playBgAudio = (url: string) => {
      if (!url) return;
      if (url !== bgAudioURL) {
        bgAudioURL = url;
        setBgAudioURL(bgAudioURL);
        bgPlayer!.changeSource(bgAudioURL);
      } else {
        // 如果当前正在播放，则不处理，否则会有破音
        if (isPlayingBgAudio.value) {
          return;
        }
        // 这里可能因为全局静音，只调用过setBgAudioURL，实质并没有在audio标签设置src
        if (bgPlayer!.getUrl()) {
          bgPlayer!.resume();
        } else {
          bgPlayer!.changeSource(bgAudioURL);
        }
      }
    };

    const pauseBgAudio = (params?: { behavior: 'smooth' | 'instant' }) => {
      bgPlayer!.pause(params);
    };

    const setAudioCurrentTime = (time: number) => {
      bgPlayer!.setCurrentTime(time);
    };

    const setAudioMuted = (muted: boolean) => {
      isAudioMuted.value = muted;
    };

    const toggleAudioMuted = () => {
      if (!isAudioMutedLoaded.value) return;
      setAudioMuted(!isAudioMuted.value);
    };

    const setPlayMode = (mode: PlayModeKey) => {
      curPlayMode.value = mode;
    };

    const setAudioList = (list: AudioItem[]) => {
      audioList.value = list;
    };

    const init = async () => {
      if (!bgPlayer) {
        bgPlayer = new AudioPlayer();
        bgPlayer.addEventListener('play', handlePlayBgAudio);
        bgPlayer.addEventListener('pause', handlePauseBgAudio);
        bgPlayer.addEventListener('ended', handleEndBgAudio);
        bgPlayer.addEventListener(
          'progress',
          handleProgressBgAudio as EventListener
        );
        bgPlayer.addEventListener('error', handleErrorBgAudio as EventListener);

        // 获取之前播放的音频URL
        curAudioURL.value = getLocalStorageItem(audioLocalKey) || '';

        // 获取是否开启静音
        const localData = getLocalStorageItem(audioMutedLocalKey);
        isAudioMuted.value = localData !== '0';

        isAudioMutedLoaded.value = true;
      }
    };

    onMounted(() => {
      init();
    });

    onBeforeUnmount(() => {
      if (bgPlayer) {
        bgPlayer.removeEventListener('play', handlePlayBgAudio);
        bgPlayer.removeEventListener('pause', handlePauseBgAudio);
        bgPlayer.removeEventListener('ended', handleEndBgAudio);
        bgPlayer.removeEventListener(
          'progress',
          handleProgressBgAudio as EventListener
        );
        bgPlayer.removeEventListener(
          'error',
          handleErrorBgAudio as EventListener
        );
        bgPlayer.destroy();
      }
      bgPlayer = null;
    });

    return {
      isAudioMutedLoaded,
      isAudioMuted,
      isAudioError: isError,
      audioURL: curAudioURL,
      audioList,
      isPlayingBgAudio,
      audioProgress: curAudioProgress,
      /**
       * 其他音频播放中（包括视频）,例如视频列表，设置了则背景音乐不播放
       */
      isOtherAudioPlaying,
      setBgAudioURL,
      playBgAudio,
      pauseBgAudio,
      setAudioMuted,
      toggleAudioMuted,
      setOtherAudioPlaying,
      setPlayMode,
      setAudioList,
      setAudioCurrentTime
    };
  }
);
