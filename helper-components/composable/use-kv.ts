import { computed, isRef, onScopeDispose, Ref, ref } from 'vue';
import { createGlobalState, useThrottleFn } from '@vueuse/core';
import jsCookie from 'js-cookie';
import { KvStatus } from '../helper-kv/kv-status';
import { getKvData, KvData } from '../helper-kv/api';
import { getLocalStorageItem, setLocalStorageItem } from '../utils/storage';

export const kv = (gameId: Ref<string> | string) => {
  const curGameId = isRef(gameId) ? gameId : ref(gameId);
  const getKvReadKey = (data: KvData) => {
    if (!data) {
      return '';
    }
    const tgpId = jsCookie.get('tgp_id');
    const contentId = data.video.split('/').pop() || '';
    return `helper_kv_isread_${curGameId.value}_${contentId}_${tgpId}`;
  };

  const curKvStatus = ref(KvStatus.pre);
  const kvStatus = computed(() => curKvStatus.value);
  const curIsLoadedKvData = ref(false);
  const kvData = ref<KvData | null>(null);
  const kvIsRead = ref(false);
  const isLoadedKvData = computed(() => curIsLoadedKvData.value);

  const KV_CLASS = {
    DEFAULT: 'default-fold',
    UNFOLD: 'slideup',
    FOLD: 'slidedown'
  }

  const kvSlideClass = ref(KV_CLASS.DEFAULT);
  const wrapClass = computed(() => {
    let cls = kvSlideClass.value;
    if ([KvStatus.fold, KvStatus.unfold].includes(curKvStatus.value)) {
      cls += ' page-container--big-suit';
    }
    return cls;
  });

  const handleMouseWheelThrottled = useThrottleFn(e => {
    if (curKvStatus.value !== KvStatus.unfold) {
      return;
    }
    if (e.wheelDelta || e.detail) {
      if (e.wheelDelta < 0 || e.detail > 0) {
        // 当鼠标滚轮向下滚动时
        goMain();
      }
    }
  }, 400);

  const foldKv = () => {
    curKvStatus.value = KvStatus.fold;
  };

  const unfoldKv = () => {
    curKvStatus.value = KvStatus.unfold;
  };

  const removeKv = () => {
    if (![KvStatus.pre].includes(curKvStatus.value)) {
      curKvStatus.value = KvStatus.none;
    }
    kvSlideClass.value = KV_CLASS.DEFAULT;
  };

  /**
   * 隐藏kv,不会再次拉取数据
   */
  const hideKv = () => {
    if (![KvStatus.pre, KvStatus.none].includes(curKvStatus.value)) {
      curKvStatus.value = KvStatus.hide;
    }
    kvSlideClass.value = KV_CLASS.DEFAULT;
  };

  /**
   * 用户已经看过该广告并滚动过页面
   * @param data
   */
  const readKv = () => {
    const cacheKey = getKvReadKey(kvData.value!);
    setLocalStorageItem(cacheKey, '1');
  };

  let animationTimeoutId: ReturnType<typeof setTimeout> | null = null;

  const clearAnimationTimeout = () => {
    if (animationTimeoutId) {
      clearTimeout(animationTimeoutId);
      animationTimeoutId = null;
    }
  };

  const goKv = () => {
    clearAnimationTimeout();
    kvSlideClass.value = KV_CLASS.UNFOLD;
    unfoldKv();
  };

  const goMain = async() => {
    clearAnimationTimeout();
    // 动画结束后重置class，该div在顶层，不在kv组件
    animationTimeoutId = setTimeout(() => {
      kvSlideClass.value = KV_CLASS.DEFAULT;
    }, 400);
    kvSlideClass.value = KV_CLASS.FOLD;
    foldKv();
    readKv();
  };

  /**
   * 是否跳转到自身助手，自身不显示手型
   */
  const isLinkSelf = computed(() => {
    const action = kvData.value?.link;
    if (action.appid && action.type === 'open_web') {
      return String(action.appid) === curGameId.value;
    }
    return false;
  });

  const openLink = () => {
    // 跳转逻辑与社区广告浮层一致
    const action = kvData.value?.link;
    if (!action) {
      console.warn('广告配置跳转错误', kvData.value);
      return;
    }
    if (action.appid && action.type === 'open_web') {
      const opt: { app: string; page?: string } = { app: action.appid };
      if (action.url) {
        opt.page = action.url;
      }
      // TODO: 临时处理跳转自身页面
      if (curGameId.value === action.appid && opt.page) {
        location.href = opt.page;
        hideKv();
      } else {
        top?.TGP?.jump(opt);
      }
    } else {
      window.external.callcpp(
        'call_service',
        JSON.stringify({
          cmd: 'i_service_open_link_confirm_launch',
          data: action,
          callback: '',
          custom_data: {}
        })
      );
    }
  };

  const fetchKvData = async () => {
    // 未拉取数据或之前数据为空，则拉取数据
    if (![KvStatus.pre, KvStatus.none].includes(curKvStatus.value)) {
      return;
    }

    curIsLoadedKvData.value = false;
    kvData.value = await getKvData(curGameId.value);
    curIsLoadedKvData.value = true;

    // 无数据
    if (!kvData.value) {
      curKvStatus.value = KvStatus.none;
      return;
    }

    const cacheReadKey = getKvReadKey(kvData.value);
    kvIsRead.value = !!Number(getLocalStorageItem(cacheReadKey));
    if (kvIsRead.value) {
      // 用户已经看过该广告并滚动过页面
      curKvStatus.value = KvStatus.fold;
      return;
    }
    goKv();
  };

  onScopeDispose(() => {
    clearAnimationTimeout();
  });

  return {
    kvStatus,
    isLoadedKvData,
    kvData,
    goMain,
    fetchKvData,
    goKv,
    removeKv,
    hideKv,
    /**
     * 是否跳转到自身助手
     */
    isLinkSelf,
    openLink,
    wrapClass,
    handleMouseWheelThrottled
  };
};

export const useKv = createGlobalState(kv);
