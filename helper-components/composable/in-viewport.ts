import { computed, ref, onBeforeUnmount } from 'vue';
import { isWeGame } from '@tencent/wegame-web-sdk';
import {
  MaybeElementRef,
  useDocumentVisibility,
  useIntersectionObserver
} from '@vueuse/core';

export function useInViewport(el: MaybeElementRef) {
  const isInViewport = ref(false);
  const isIframeHidden = ref(false);
  const documentVisibility = useDocumentVisibility();
  const returnValue = computed(
    () =>
      isInViewport.value &&
      !isIframeHidden.value &&
      documentVisibility.value === 'visible'
  );
  let iframeDisplayOb: null | MutationObserver = null;

  useIntersectionObserver(
    el,
    ([{ isIntersecting }]) => {
      isInViewport.value = isIntersecting;
    },
    {
      threshold: [0]
    }
  );

  if (isWeGame) {
    const iframeEl = window.frameElement as HTMLElement;
    iframeDisplayOb = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.attributeName === 'style') {
          isIframeHidden.value =
            (mutation.target as HTMLElement).style.display === 'none';
        }
      });
    });
    iframeDisplayOb.observe(iframeEl, {
      attributeFilter: ['style']
    });
  }

  onBeforeUnmount(() => {
    iframeDisplayOb?.disconnect();
    iframeDisplayOb = null;
  });

  return returnValue;
}
