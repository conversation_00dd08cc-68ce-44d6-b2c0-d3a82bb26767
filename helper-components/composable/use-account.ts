import { ref, computed, onMounted } from 'vue';
import { createGlobalState, useAsyncState } from '@vueuse/core';
import { cookie } from '@tencent/wegame-web-sdk';
import { btGetUserInfo } from '../api/user-info-api';

export const useLoginUser = createGlobalState(() => {
  const { state } = useAsyncState(async () => {
    const tgpId = cookie.get('tgp_id') || '';
    const list = await btGetUserInfo({
      uid: tgpId,
      dst_list: tgpId ? [tgpId] : []
    });
    console.log('list', list);
    const item = list.find(item => item.uid === tgpId);
    return item;
  }, null);

  return {
    loginUser: state
  };
});

export const enum ACCOUNT_CHANNEL {
  UNKNOWN = -1,
  QQ = 0,
  WX = 1
}

export const enum ACCOUNT_PLATFORM {
  UNKNOWN = -1,
  iOS = 0,
  ANDROID = 1
}

/**
 * 无效区服ID
 */
export const INVALID_AREA_ID = -1;

export const useAccount = createGlobalState(() => {
  const isLoadedArea = ref(false);
  const curAreaId = ref(INVALID_AREA_ID);

  const areaMapping = ref({});

  const setAreaMapping = (
    mapping: Record<
      number,
      { channel: ACCOUNT_CHANNEL; platform: ACCOUNT_PLATFORM }
    >
  ) => {
    areaMapping.value = mapping;
  };

  onMounted(() => {
    curAreaId.value = top?.TGP?.status?.aid || INVALID_AREA_ID;
    isLoadedArea.value = true;
    (window as any).switchZone = (aid: number) => {
      curAreaId.value = aid;
    };
  });

  return {
    areaId: computed(() => curAreaId.value),
    isLoadedArea: computed(() => isLoadedArea.value),
    setAreaMapping,
    channel: computed<ACCOUNT_CHANNEL>(
      () =>
        areaMapping.value[curAreaId.value]?.channel ?? ACCOUNT_CHANNEL.UNKNOWN
    ),
    platform: computed<ACCOUNT_PLATFORM>(
      () =>
        areaMapping.value[curAreaId.value]?.platform ?? ACCOUNT_PLATFORM.UNKNOWN
    )
  };
});
