import { ref, onMounted, onUnmounted, computed } from 'vue';

export const useIframeDisplay = () => {
  const frameEl = ref<HTMLIFrameElement | null>(null);
  const isVisible = ref(true);
  let observer: MutationObserver | null = null;

  // 初始化获取iframe元素
  onMounted(() => {
    frameEl.value = window.frameElement as HTMLIFrameElement;
    if (!frameEl.value) return;

    // 初始可见状态
    isVisible.value = frameEl.value.style.display !== 'none';

    // 创建MutationObserver监听样式变化
    observer = new MutationObserver(() => {
      if (frameEl.value) {
        isVisible.value = frameEl.value.style.display !== 'none';
      }
    });

    observer.observe(frameEl.value, {
      attributes: true,
      attributeOldValue: true,
      attributeFilter: ['style']
    });
  });

  // 清理observer
  onUnmounted(() => {
    if (observer) {
      observer.disconnect();
    }
  });

  return {
    iframeVisible: computed(() => isVisible.value)
  };
};
