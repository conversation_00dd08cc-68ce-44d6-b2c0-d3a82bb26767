<template>
  <div v-if="visible" class="helper-kv-entry">
    <a href="javascript:;" class="helper-kv-entry-link" @click="clickOpenLink">
      <slot></slot>
    </a>
    <span class="helper-kv-entry-row" @click="clickGoKv"></span>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { useMounted } from '@vueuse/core';
import { useKv } from '../composable/use-kv';
import { reportEvent } from '../utils/report';
import { KvStatus } from './kv-status';

const props = defineProps<{
  appId: string;
}>();

const isMounted = useMounted();
const {kvStatus,  goKv, openLink } = useKv(props.appId);

const visible = computed(() => {
  return (
    isMounted.value && ![KvStatus.pre, KvStatus.none].includes(kvStatus.value)
  );
});

const clickGoKv = () => {
  goKv();
  reportEvent({
    appId: props.appId,
    block: 'helper_kv_entry',
    action: 'go_kv'
  });
};

const clickOpenLink = () => {
  openLink();
  reportEvent({
    appId: props.appId,
    block: 'helper_kv_entry',
    action: 'open_page'
  });
};
</script>
