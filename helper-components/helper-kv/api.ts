import { baseRequest } from '@tencent/wegame-web-sdk';
import { ColorMode } from '../constant/color-mode';

export interface KvData {
  data_name: string;
  title: string;
  visible: boolean | string;
  video: string;
  bg: string;
  colorMode: ColorMode;
  themeColor: string;
  audio?: string;
  poster: string;
  /**
   * 社区浮层广告跳转配置
   */
  link?: any;
  jump_url?: string;
  jump_url_type?: 'system' | 'new_wegame_window' | 'direct';
  valid_time: {
    start_time: number;
    end_time: number;
  };
}

export const getKvData = async (gameId: string) => {
  try {
    const res = await baseRequest<{
      prompt_list: {
        prompts: {
          task: {
            task_id: number;
            title: string;
            link: string;
            material_json_info: string;
          };
        }[];
      }[];
    }>({
      method: 'POST',
      url: '/api/v1/wegame.rail.game.PromptMarket/QueryPromptMarket',
      data: {
        game_id: gameId,
        pos_id_list: [28000], // 社区浮层--自定义助手KV专用
        from_src: 'marketfloat'
      }
    });

    const prompt = res?.prompt_list?.[0]?.prompts?.[0];
    if (prompt) {
      const { task } = prompt;
      const newItem = JSON.parse(task.material_json_info);
      newItem.item_id = task.task_id;
      newItem.title = task.title || '';
      newItem.link = JSON.parse(task.link);
      newItem.colorMode =
        newItem.colorMode === '1' ? ColorMode.dark : ColorMode.light;
      return newItem as KvData;
    }
  } catch (err) {
    console.error('getPromptMarketData failed:', err);
  }
  return null;
};
