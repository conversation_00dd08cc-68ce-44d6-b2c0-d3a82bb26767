<template>
  <template v-if="modelValue">
    <Teleport to="body">
      <div :class="`helper-toast toast-position-${position || 'center'}`">
        <div
          class="helper-toast-ele"
          :style="`animation-duration: ${duration || 3000}ms;`"
          @animationend="emits('update:modelValue', false)"
        >
          <template v-if="!$slots.custom">
            <span
              v-if="icon"
              :class="`helper-toast-icon toast-icon-${icon}`"
            ></span>
            <div v-if="text" class="helper-toast-text">
              {{ text }}
            </div>
            <div v-if="$slots.default" class="helper-toast-text">
              <slot></slot>
            </div>
          </template>
          <div v-if="$slots.custom" class="helper-toast-cont">
            <slot name="custom"></slot>
          </div>
        </div>
        <div v-if="preventClick" class="helper-toast-mask"></div>
      </div>
    </Teleport>
  </template>
</template>
<script setup lang="ts">
import './index.scss';
const emits = defineEmits(['update:modelValue']);

defineProps<{
  modelValue: boolean;
  preventClick?: false; // 是否阻止点击
  text?: string;
  position?: 'center' | 'bottom' | 'auto';
  icon?: 'info' | 'success' | 'error';
  duration?: number; // toast显示持续时间
}>();
</script>
