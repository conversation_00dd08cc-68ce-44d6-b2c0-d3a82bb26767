// 通过api添加oss模板
async function addToOss(type, options) {
  const urls = {
    template: 'http://test.oss.s.tgp.oa.com/template/add',
    element: 'http://test.oss.s.tgp.oa.com/template/add_element'
  };
  const response = await fetch(urls[type], {
    method: 'POST',
    mode: 'cors',
    cache: 'no-cache',
    credentials: 'same-origin',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(options)
  });
  const res = await response.json();
  if (type === 'template' && res.data?.tid) {
    return res.data.tid;
  }
  return false;
}
