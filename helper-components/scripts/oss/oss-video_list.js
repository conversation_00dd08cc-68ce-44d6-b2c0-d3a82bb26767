const GAME_ID = '2002137';
const GAME_NAME = '鸣潮';
const TEMPLATE_NAME = `helper_components_video_list_${GAME_ID}`;
const TEMPLATE_DISPLAY_NAME = `助手精彩视频-${GAME_NAME}`;

const spans = document.getElementsByTagName('span');
const gameListSpan = Array.from(spans).find(span => span.textContent.trim() === '定制游戏助手');
const gameSpans = gameListSpan.parentElement.parentElement.getElementsByTagName('span');
const gameSpan = Array.from(gameSpans).find(span => span.textContent.trim() === GAME_NAME);
const pid = gameSpan.id.replace('sidebar_tid_', '');
if(!pid) return; 

// 添加的模板
const tParams = {
  type: 1,
  pid,
  name: TEMPLATE_NAME,
  display_name: TEMPLATE_DISPLAY_NAME
};

// 添加的字段
const eParams = [
  {
    type: 1,
    name: 'vid',
    display_name: '视频vid',
    comment: '',
    required: 1,
    position: 2,
    validation: '',
    value: ''
  },
  {
    type: 1,
    name: 'title',
    display_name: '标题',
    comment: '',
    required: 1,
    position: 3,
    validation: '',
    value: ''
  },
  {
    type: 1,
    name: 'tab',
    display_name: 'tab',
    comment: '格式为${index}-${tabName}，例如1-全部，全部子项都不填则不显示tab',
    required: 0,
    position: 4,
    validation: '',
    value: ''
  },
  {
    type: 9,
    name: 'cover',
    display_name: '视频封面图',
    comment: '',
    required: 1,
    position: 5,
    validation: '',
    value: ''
  },
  {
    type: 1,
    name: 'author',
    display_name: '视频作者',
    comment: '',
    required: 0,
    position: 6,
    validation: '',
    value: ''
  },
  {
    type: 11,
    name: 'publish_time',
    display_name: '视频发布时间',
    comment: '时间格式: YYYY-MM-DD HH:mm:ss',
    required: 0,
    position: 7,
    validation: '',
    value: ''
  },
  {
    type: 4,
    name: 'is_new',
    display_name: '是否NEW',
    comment: '',
    required: 0,
    position: 8,
    validation: '',
    value: '[{"name":"是","value":"1"},{"name":"否","value":"0"}]'
  },
  {
    type: 3,
    name: 'order',
    display_name: '展示顺序',
    comment: '升序',
    required: 0,
    position: 9,
    validation: '',
    value: ''
  }
];

// 添加模板
const tid = await addToOss('template', tParams);
// 添加字段
if (tid) {
  eParams.forEach(p => {
    addToOss('element', { ...p, tid });
  });
}
