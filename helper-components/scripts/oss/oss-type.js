// OSS常用类型，其他类型在test建个个人专属的OSS抓包测试 

// 跳转
const eParams = [
    {
        type: 1,
        name: 'jump_appid',
        display_name: '跳转appid',
        comment:
            '不填或0为打开浏览器， 50000013商店|50000014个人主页|50000015网吧专区|50000019测试专区|2001068壁纸工坊',
        required: 0,
        position: 4,
        validation: '',
        value: ''
    },
    {
        type: 7,
        name: 'jump_url',
        display_name: '跳转链接',
        comment: '',
        required: 1,
        position: 5,
        validation: '',
        value: ''
    },
    {
        type: 4,
        name: 'jump_url_type',
        display_name: '跳转链接类型',
        comment: '',
        required: 0,
        position: 6,
        validation: '',
        value:
            '[{"name":"系统浏览器","value":"system"},{"name":"WeGame浏览器","value":"new_wegame_window"},{"name":"直接跳转","value":"direct"}]'
    }
];

const typeParams = [
    {
        type: 1, // 短文本
        name: 'vid',
        display_name: '视频vid',
        comment: '',
        required: 1,
        position: 2,
        validation: '',
        value: ''
    },
    {
        type: 3, // 数值
        name: 'order',
        display_name: '展示顺序',
        comment: '升序',
        required: 0,
        position: 9,
        validation: '',
        value: ''
    },
    {
        type: 4, // 下拉单选
        name: 'is_new',
        display_name: '是否NEW',
        comment: '',
        required: 0,
        position: 8,
        validation: '',
        value: '[{"name":"是","value":"1"},{"name":"否","value":"0"}]'
    },
    {
        type: 7, // URL
        name: 'jump_url',
        display_name: '跳转链接',
        comment: '',
        required: 1,
        position: 5,
        validation: '',
        value: ''
    },
    {
        type: 9, // 图片
        name: 'cover',
        display_name: '视频封面图',
        comment: '',
        required: 1,
        position: 5,
        validation: '',
        value: ''
    },
    {
        type: 11, // 时间点
        name: 'publish_time',
        display_name: '视频发布时间',
        comment: '时间格式: YYYY-MM-DD HH:mm:ss',
        required: 0,
        position: 7,
        validation: '',
        value: ''
    },
    {
        type: 12, // 时间范围
        name: 'publish_time',
        display_name: '时间段',
        comment: '',
        required: 0,
        position: 9,
        validation: '',
        value: ''
    }
]