<template>
  <div
    ref="container"
    :data-appid="appId"
    data-block="fab"
    class="floating-action-buttons"
  >
    <!-- 配置单个按钮 S  -->
    <div
      v-for="item in showMenus"
      :key="item.title"
      :class="`floating-action-button floating-action-button--${item.cssClass}`"
    >
      <div
        class="floating-action-button-inner"
        @click.stop="item?.url ? clickItem(item) : ''"
      >
        <span class="floating-action-icon">
          <img
            v-if="item.qrcodeIcon"
            class="floating-action-icon-xj"
            :src="item.qrcodeIcon"
            alt=""
          />
          <div class="floating-action-qrcode">
            <img v-if="item.qrcode" :src="item.qrcode" alt="" />
          </div>
        </span>
        <span class="floating-action-text">{{ item.title }}</span>
      </div>
    </div>
    <!-- 配置单个按钮 E  -->
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';

import { reportEvent, reportExposure } from '../utils/report';
import { getFabData } from './api';

const enum FabType {
  qrcode, // 二维码
  qqGroup, // QQ群
  customerService // 客服
}

interface FabItem {
  type: FabType;
  title: string;
  cssClass: string;
  url?: string;
  qrcode?: string;
  qrcodeIcon?: string;
}

const props = defineProps<{
  appId: string;
}>();

const container = ref(null);

const configData = ref<{
  qrcode?: string;
  qrcode_icon?: string;
  qq_group_url?: string;
  customer_service_url?: string;
} | null>(null);

const menus = {
  [FabType.qrcode]: {
    title: '二维码',
    cssClass: 'qrcode'
  },
  [FabType.qqGroup]: {
    title: 'qq',
    cssClass: 'qq'
  },
  [FabType.customerService]: {
    title: '客服',
    cssClass: 'services'
  }
};

const showMenus = computed<FabItem[]>(() => {
  const list = [];
  if (configData.value?.qrcode && configData?.value.qrcode_icon) {
    list.push({
      ...menus[FabType.qrcode],
      type: FabType.qrcode,
      qrcode: configData.value.qrcode,
      qrcodeIcon: configData.value.qrcode_icon
    });
  }
  if (configData.value?.qq_group_url) {
    list.push({
      type: FabType.qqGroup,
      url: configData.value.qq_group_url,
      ...menus[FabType.qqGroup]
    });
  }
  if (configData.value?.customer_service_url) {
    list.push({
      type: FabType.customerService,
      url: configData.value.customer_service_url,
      ...menus[FabType.customerService]
    });
  }

  return list;
});

const clickItem = (item: FabItem) => {
  // 系统浏览器打开
  window.open(item.url, '_blank', 'fullscreen=yes');
  reportEvent({
    appId: props.appId,
    block: 'floating_action_button',
    action: 'open_link',
    ext: item.type
  });
};

onMounted(async () => {
  configData.value = await getFabData(props.appId);
  if (container.value) {
    reportExposure([container.value]);
  }
});
</script>

<style lang="scss">
@use './assets/scss/floating-action-buttons.scss';
</style>
