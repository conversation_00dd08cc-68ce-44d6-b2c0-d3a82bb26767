<template>
  <li class="video-list-item">
    <!-- 视频卡片 S  -->
    <div class="video-card">
      <!-- 视频卡片封面区域 S  -->
      <div class="video-card-cover">
        <div class="video-card-image">
          <!-- 视频卡片封面图 S  -->
          <img class="video-card-img" :alt="item.title" :src="item.cover" />
          <!-- 视频卡片封面图 E  -->
        </div>
        <!-- 视频播放图标--无事件交互 S  -->
        <span class="video-card-play-icon"></span>
        <!-- 视频播放图标--无事件交互 E  -->

        <!-- 视频播放时长 S  -->
        <span class="video-card-play-duration"></span>
        <!-- 视频播放时长 E  -->
      </div>
      <!-- 视频卡片封面区域 E  -->

      <!-- 视频摘要信息 S  -->
      <div class="video-card-info">
        <div class="video-card-title">
          <!-- 标题  -->
          <span class="video-card-title-text">{{ item.title }}</span>
        </div>
        <div class="video-card-meta">
          <!-- 来源 -->
          <span v-if="item.author" class="video-card-source">
            {{ item.author }}
          </span>

          <!-- 时间 -->
          <span v-if="publishTimeTxt" class="video-card-time">
            {{ publishTimeTxt }}
          </span>

          <!-- NEW 标签 -->
          <span v-if="item.isNew" class="video-card-tag tag-new">NEW</span>
        </div>
      </div>
      <!-- 视频摘要信息 E  -->
    </div>
    <!-- 视频卡片 E  -->
  </li>
</template>
<script lang="ts" setup>
import { computed } from 'vue';

import { VideoItem } from './interface';

const props = defineProps<{
  item: VideoItem;
}>();

const publishTimeTxt = computed(() => {
  return props.item.publishTime.format('MM-DD');
});
</script>
