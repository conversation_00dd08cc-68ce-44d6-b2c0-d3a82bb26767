// @video-card

.video-card {
  &:hover {
    .video-card-cover {
      cursor: pointer;
    }
  }
}
.video-card-cover {
  height: 180px; // 初始值，若不合适，建议业务侧重置
  position: relative;
  z-index: 10;
  border-radius: 6px;
  overflow: hidden;
}
.video-card-image {
  width: 100%;
  height: 100%;
  position: relative;
}
.video-card-img {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}
.video-card-play-icon {
  width: 64px;
  height: 64px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -32px 0 0 -32px;
  z-index: 5;
  cursor: pointer;
}
.video-card-play-duration {
}

.video-card-info {
  font-size: 14px;
  padding-top: 10px;
}
.video-card-title {
}
.video-card-title-text {
  font-size: 16px;
  cursor: pointer;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  text-overflow: ellipsis;
}

.video-card-meta {
  display: flex;
  align-items: center;
}
.video-card-source {
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.video-card-time {
  position: relative;
}

.video-card-source + .video-card-time {
  margin-left: 14px;
  &::before {
    content: '';
    width: 1px;
    height: 62%;
    position: absolute;
    top: 18%;
    left: -8px;
    background-color: rgba($color: #000000, $alpha: 0.5);
  }
}
.video-card-tag {
  font-size: 12px;
  font-weight: bold;
}
.video-card-time + .video-card-tag {
  margin: 0 0 0 8px;
}
// @video-list
.video-list-item {
  margin-bottom: 34px;
  margin-right: 20px;
}
.video-list--cols-3 .video-list-inner {
  display: grid;
  grid-template-columns: 33.33% 33.33% 33.33%;
  margin-right: -20px;
}
.video-list--cols-2 .video-list-inner {
  display: grid;
  grid-template-columns: 50% 50%;
  margin-right: -20px;
}
.helper-panel-header {
  padding: 0 0 10px 0;
  display: flex;
}
.helper-panel-title-text {
  font-size: 30px;
  font-weight: bold;
}
.helper-panel-tools {
  flex: 1;
  width: 100%;
}

.panel-tools-tab {
  overflow: hidden;
  padding-left: 20px;
  height: 40px;
}
.panel-tools-tab-item {
  font-size: 16px;
  font-weight: normal;
  color: inherit;
  padding: 0 15px;
  height: 40px;
  line-height: 40px;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}
.panel-tools-tab-menu {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  &.current {
    .panel-tools-tab-item {
      font-weight: bold;
    }
  }
}
