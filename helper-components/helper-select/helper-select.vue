<template>
  <div
    class="helper-select"
    :class="classes"
    :style="selectStyles"
    @mouseenter="handleMouseEvent('enter')"
    @mouseleave="handleMouseEvent('leave')"
  >
    <div class="helper-select-input" @click="handleClick">
      <span class="helper-select-placeholder">
        {{ currentLabel || placeholder }}
      </span>
    </div>
    <div ref="selectMenuRef" class="helper-select-dropdown" :style="menuStyles">
      <div class="helper-dropdown-bd">
        <ul class="helper-dropdown-list">
          <slot></slot>
        </ul>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" generic="T">
import { computed, provide, ref, toRef } from 'vue';
import { onClickOutside } from '@vueuse/core';
import { selectKey, type SelectOptionInjectionKey } from './select';
const emit = defineEmits(['update:modelValue', 'labelChange']);
const props = withDefaults(
  defineProps<{
    modelValue: T;
    disabled?: boolean;
    noBorder?: boolean;
    placeholder?: string;
    dropType?: 'click' | 'hover';
    width?: string | number;
  }>(),
  {
    disabled: false,
    noBorder: false,
    dropType: 'click',
    placeholder: '',
    width: 'auto'
  }
);

const visible = ref(false);
const currentLabel = ref<string | number>('');
const modelValueRef = toRef(props, 'modelValue');
const handleMouseEvent = (eventType: 'enter' | 'leave') => {
  if (props.dropType === 'click') return;
  if (eventType === 'enter') {
    visible.value = true;
  } else {
    visible.value = false;
  }
};
const menuStyles = computed(() => {
  return [{ display: visible.value ? 'block' : 'none' }];
});

const selectStyles = computed(() => {
  let widthValue = props.width;
  if (typeof props.width === 'number') {
    widthValue = `${props.width}px`;
  }
  return [{ width: widthValue }];
});

const handleClick = () => {
  // 假如slot无内容侧取label值,假如都无则取value
  toggleMenu();
};

const toggleMenu = () => {
  if (props.disabled) return;
  visible.value = !visible.value;
};

const hideMenu = () => {
  visible.value = false;
};

const selectMenuRef = ref<HTMLElement | null>(null);
onClickOutside(selectMenuRef, hideMenu);

const updateCurrentLabel = (data: string | number) => {
  currentLabel.value = data;
};

const onSelectItem = (data: { value: T; label: string | number }) => {
  currentLabel.value = data.label;
  emit('update:modelValue', data.value);
  emit('labelChange', data.label);
  hideMenu();
};

provide(selectKey as SelectOptionInjectionKey<T>, {
  modelValueRef,
  updateCurrentLabel,
  onSelectItem
});

const classes = computed(() => {
  return [
    {
      'helper-select--hover': props.dropType === 'hover',
      expand: visible.value,
      disabled: props.disabled
    }
  ];
});
</script>
<style lang="scss">
@use './index.scss';
</style>
