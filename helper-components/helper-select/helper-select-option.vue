<template>
  <li class="helper-dropdown-item">
    <a
      class="helper-dropdown-menu"
      :class="classes"
      href="javascript:void(0);"
      @click="handleSelect"
    >
      <slot>
        <span>{{ labelText }}</span>
      </slot>
    </a>
  </li>
</template>
<script setup lang="ts" generic="T">
import { inject, ref, computed, watchEffect } from 'vue';
import { type SelectOptionInjectionKey, selectKey } from './select';

const props = defineProps<{
  value: T;
  label: string | number;
}>();

const selectGroup = inject(selectKey as SelectOptionInjectionKey<T>);
const optionValueData = ref<{
  value: T;
  label: string | number;
}>();

const labelText = computed(() => {
  return props.label ? props.label : props.value;
});

const initCurrentLabel = () => {
  selectGroup?.updateCurrentLabel(props.label);
};

const classes = computed(() => {
  return { current: props.value === selectGroup?.modelValueRef.value };
});

const handleSelect = () => {
  optionValueData.value = {
    value: props.value,
    label: props.label || props.value?.toString() || ''
  };
  selectGroup?.onSelectItem(optionValueData.value);
};

watchEffect(() => {
  if (selectGroup?.modelValueRef.value === props.value) {
    initCurrentLabel();
  }
});
</script>
