<template>
  <div class="helper-news">
    <div v-if="tabList.length > 0 || moreURL" class="news-header">
      <div class="news-tab">
        <div
          v-for="(item, index) in tabList"
          :key="item.name"
          class="tab-item"
          :class="[{ current: curTabIndex === index }]"
          @click="clickChangeIndex(index)"
        >
          {{ item.name }}
        </div>
        <div class="news-tab-line"></div>
      </div>
      <div class="news-tools">
        <div v-if="moreURL" class="news-more" @click="clickMore(moreURL)">
          更多
        </div>
      </div>
    </div>
    <div class="news-list">
      <template v-if="curTab?.list.length">
        <div
          v-for="item in curTab.list.slice(0, showCount)"
          :key="item.title"
          class="news-item"
          @click="clickInfo(item)"
        >
          <div class="news-title">
            <span v-if="false" class="news-tag">最新</span>
            <div class="news-title-text">
              {{ item.title }}
            </div>
          </div>
          <div class="news-time">{{ formatTime(item.release_time) }}</div>
        </div>
      </template>
      <HelperNoData v-else :with-icon="false" text="暂无数据" />
    </div>
  </div>
</template>
<script setup lang="ts">
import './index.scss';
import { computed, ref, watch } from 'vue';
import { useAsyncState } from '@vueuse/core';
import dayjs from 'dayjs';
import HelperNoData from './../ui/helper-nodata/index.vue';
import { getInfoData, InfoItem } from '../api/crawler-api';
import { getInfoData as getOssInfoData } from './api';
import { jumpPage } from '../utils/jump';
import { reportEvent } from '../utils/report';

const props = withDefaults(
  defineProps<{
    gameId: string;
    /**
     * internal 默认组件内部处理数据，等同于 crawler
     */
    dataSource?: 'internal' | 'external' | 'oss' | 'crawler';
    /**
     * 最多显示个数
     */
    showCount?: number;
    /**
     * 时间格式化模板
     */
    timeTemplate?: string;
    /**
     * 跳转更多URL
     */
    more?: string;
  }>(),
  {
    dataSource: 'internal',
    showCount: 3,
    timeTemplate: 'MM-DD',
    more: '',
  }
);

const { state, execute } = useAsyncState(
  async () => {
    if (props.dataSource === 'crawler') {
      return getInfoData(props.gameId);
    } else if (props.dataSource === 'oss') {
      return getOssInfoData({ gameId: props.gameId });
    }
    return { more: { jump_url: '' }, tabs: [] };
  },
  { more: { jump_url: '' }, tabs: [] },
  {
    immediate: false,
    resetOnExecute: false
  }
);

const moreURL = computed(() => {
  return props.more || state.value?.more?.jump_url || '';
});

const curTabIndex = ref(0);

const tabList = computed(() => {
  return state.value?.tabs || [];
});

const curTab = computed(() => tabList.value[curTabIndex.value]);

const formatTime = (time: string) => {
  if (time) {
    try {
      return dayjs(time).format(props.timeTemplate);
    } catch (error) {
      console.error(error);
    }
  }
  return '';
};

const clickChangeIndex = (index: number) => {
  curTabIndex.value = index;
  reportEvent({
    appId: props.gameId,
    block: 'info',
    action: 'click_tab',
    ext: curTab.value.name
  });
};

const clickInfo = (item: InfoItem) => {
  jumpPage({
    jump_url: item.jump_url,
    jump_url_type: 'new_wegame_window'
  });
  reportEvent({
    appId: props.gameId,
    block: 'info',
    action: 'click_info',
    ext: item.title
  });
};

const clickMore = (url: string) => {
  jumpPage({
    jump_url: url,
    jump_url_type: 'new_wegame_window'
  });
  reportEvent({
    appId: props.gameId,
    block: 'info',
    action: 'click_more'
  });
};

watch(
  () => props.dataSource,
  () => {
    execute();
  },
  {
    immediate: true
  }
);
</script>
