<template>
  <div v-if="isShowPopup" ref="el" class="gift-popup">
    <div class="gift-popup-content">
      <h3 class="gift-popup-title"></h3>
      <p class="gift-popup-des">
        <span>下载进度达到</span>
        <span class="gift-popup-des-num"
          >{{ giftList[0].achevied_prerequisites }}%<i
            class="gift-popup-des-dot"
          ></i
          >{{ giftList[1].achevied_prerequisites }}%<i
            class="gift-popup-des-dot"
          ></i
          >{{ giftList[2].achevied_prerequisites }}%</span
        >
        <span>即可领取三份限定礼包</span>
      </p>
      <div class="gift-popup-table">
        <div
          v-for="gift in giftList"
          :key="gift.gift_id"
          class="gift-popup-table-item"
        >
          <img
            :src="gift.gift_url"
            :alt="gift.name"
            class="gift-popup-table-pic"
          />
          <div :title="gift.name" class="gift-popup-table-name">{{
            gift.name
          }}</div>
        </div>
      </div>
      <div class="gift-popup-btn-box">
        <a
          href="javascript:;"
          class="gift-popup-btn"
          @click.prevent="emits('hide')"
          ><span class="gift-popup-btn-text">好的</span></a
        >
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import jsCookie from 'js-cookie';
import { ref, computed } from 'vue';
import { Gift, getConfig, setConfig } from './api';
import { reportEvent } from '../utils/report';

const props = defineProps<{
  giftList: Gift[];
  appId: string;
}>();
const emits = defineEmits<(e: 'hide') => void>();

const hasShownPopup = ref(true);

const getFormattedDate = (t: number) => {
  const date = new Date(t);
  const year = date.getFullYear();
  const month = `0${date.getMonth() + 1}`.slice(-2);
  const day = `0${date.getDate()}`.slice(-2);

  return `${year}-${month}-${day}`;
};

const isShowPopup = computed(
  () => props.giftList.length && !hasShownPopup.value
);

const init = async () => {
  const tgpId = jsCookie.get('tgp_id')!;

  const { value } = await getConfig({ tgpId });
  const today = getFormattedDate(Date.now());

  if (value === today) {
    // 今天展示过了就不再展示
    return;
  }
  hasShownPopup.value = false;

  if (props.giftList.length) {
    reportEvent({
      appId: props.appId,
      block: 'gift_adv_popup',
      action: 'expose'
    });
    setConfig({
      tgpId,
      value: today
    });
  }
};
init();
</script>
