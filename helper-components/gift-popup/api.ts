import { service } from '@tencent/wegame-web-sdk';

export enum GiftStatus {
  Unknown = 0,
  CannotReceive, // 未达到条件
  CanReceive, // 未领取
  Received // 已领取
}

export interface Gift {
  name: string; // 礼品名称
  gift_url: string; // 礼品图片
  game_id: number;
  gift_status: GiftStatus;
  gift_id: number; // 礼品id
  achevied_prerequisites: number; // 礼品达成条件，比如进度条达到多少
}

const CONFIG_KEY = 'helper_la_gift_popup_last_date';

export function getConfig({ tgpId }: { tgpId: string }): Promise<{
  value: string;
  exits: 0 | 1;
}> {
  return new Promise(resolve => {
    service.call(
      'Svr_GetConfig',
      {
        common: 0,
        key: CONFIG_KEY,
        account: tgpId
      },
      (data: any) => {
        resolve(data);
      }
    );
  });
}

export function setConfig({ tgpId, value }: { tgpId: string; value: string }) {
  service.call('Svr_SetConfig', {
    common: 0,
    key: CONFIG_KEY,
    account: tgpId,
    value
  });
}
