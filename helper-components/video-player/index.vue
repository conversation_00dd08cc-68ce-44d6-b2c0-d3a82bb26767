<template>
  <div ref="wrapper" style="width: 100%; height: 100%">
    <div
      :id="domId"
      :class="
        typeof customClass === 'string'
          ? customClass
          : (customClass || []).join(' ')
      "
      style="height: 100%; width: 100%"
    ></div
  ></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, watch, ref } from 'vue';
import SuperPlayer, { type ISuperPlayerConfig } from '@tencent/super-player';
import { initPlayer } from '../utils/init-superplayer';
import { useInViewport } from '../composable/in-viewport';

const props = defineProps<{
  domId: string;
  vid?: string; // 腾讯视频vid
  url?: string; // mp4
  play: boolean;
  customClass?: string | string[];
  offScreenPause?: boolean;
  config?: Omit<ISuperPlayerConfig, 'container'>;
  volume?: number;
  disableAutoplay?: boolean;
}>();
const emit = defineEmits<{
  (e: 'timeupdate', time: number): void;
  (e: 'update:play', play: boolean): void;
  (e: 'end'): void;
}>();

let player: null | SuperPlayer = null;
let ob: null | IntersectionObserver = null;
const wrapper = ref<HTMLElement | null>(null);

watch(
  () => props.play,
  val => {
    if (val) {
      playVideo();
    } else {
      player?.pause();
    }
  }
);

watch(
  () => props.vid,
  async val => {
    if (!val) {
      return;
    }
    player?.destroy();
    player = null;
    playVideo();
  }
);

watch(
  () => props.url,
  async val => {
    if (!val) {
      return;
    }
    player?.destroy();
    player = null;
    playVideo();
  }
);

const execPlayVideo = () => {
  if (!player) return;
  if (props.vid) {
    if (props.disableAutoplay) {
      player.load(
        {
          vid: props.vid
        },
        { videoReqInfoParam: { defn: 'fhd' } } // vid 1080p播放  sd hd shd fhd
      );
    } else {
      player.play(
        {
          vid: props.vid
        },
        { videoReqInfoParam: { defn: 'fhd' } } // vid 1080p播放  sd hd shd fhd
      );
    }
  }

  if (props.url) {
    player.play({
      url: props.url
    });
  }
};

const playVideo = async () => {
  if (player) {
    player.play();
    return;
  }
  try {
    player = await initPlayer({
      ...(props.config || {}),
      container: `#${props.domId}`
    });
    if (!player) {
      return;
    }
    execPlayVideo();

    // 默认做loop播放
    const isLoop = props.config?.loop === false ? props.config.loop : true;

    player.on('VIDEO_TIMEUPDATE', evt => {
      emit('timeupdate', evt.data?.playtime || 0);
    });

    player.on('VIDEO_PLAYING', () => {
      emit('update:play', true);
    });
    player.on('VIDEO_END', code => {
      // 只有视频正常结束才触发事件
      if (code.data.endState === 'normal') {
        emit('end');

        if (isLoop) {
          emit('update:play', true);
          execPlayVideo();
        } else {
          emit('update:play', false);
        }
      }
    });
    player.on('VIDEO_PAUSE', () => {
      emit('update:play', false);
    });

    const volume = props.volume || 0;
    if (volume) {
      player.volume = volume;
    }
  } catch (error) {
    console.error(error);
  }
};

onMounted(() => {
  props.play && playVideo();

  if (props.offScreenPause) {
    const isInViewport = useInViewport(wrapper.value!);

    watch(
      () => isInViewport.value,
      val => {
        if (val) {
          playVideo();
        } else {
          player?.pause();
        }
        emit('update:play', val);
      }
    );
  }
});

onBeforeUnmount(() => {
  player?.destroy();
  player = null;
  ob?.disconnect();
  ob = null;
});
</script>
