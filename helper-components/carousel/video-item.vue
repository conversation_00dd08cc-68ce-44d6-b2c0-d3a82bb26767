<template>
  <div ref="container" class="carousel-pic">
    <Transition name="image-transition">
      <div v-show="src" :class="classes">
        <video ref="videoEl" preload="none" loop muted @error="onError">
          <source ref="sourceEl" :src="src" />
        </video>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, toRefs, computed, onMounted } from 'vue';

const props = defineProps<{
  index: number;
  title: string;
  src: string;
}>();

const container = ref<HTMLElement | null>(null);
const videoEl = ref<HTMLVideoElement | null>(null);
const sourceEl = ref<HTMLSourceElement | null>(null);

const { src } = toRefs(props);

const isError = ref(false);

const onError = () => {
  isError.value = true;
};

const classes = computed(() => ({
  'carousel-pic-image': true,
  error: isError.value
}));

onMounted(() => {
  if (container.value) {
    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          if (sourceEl.value) {
            if (!sourceEl.value.src) {
              sourceEl.value.src = props.src;
            }
          }
          if (videoEl.value) {
            videoEl.value.play();
          }
        } else {
          if (videoEl.value) {
            videoEl.value.pause();
          }
        }
      });
    });
    observer.observe(container.value);
  }
});
</script>
