@charset "utf-8";
@use './path-rem.scss';

:root {
  --carousel-color-white-op-1: rgba(255, 255, 255, 0.1);
  --carousel-color-brand1: #5fcdf5;
}
.theme-cn-dark {
  --carousel-color-white-op-1: rgba(255, 255, 255, 0.1);
  --carousel-color-brand1: #5fcdf5;
}
.carousel {
  height: 320px;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  background-color: var(--carousel-color-white-op-1);
  font-size: 0;
  &.carousel-fill {
    height: 100%;
  }
}
.carousel-wrapper {
  height: 100%;
  transition: all 0.3s ease-out;
}
.carousel-item {
  width: 100%;
  height: 100%;
  display: inline-block;
  cursor: pointer;
  position: relative;
  video {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.carousel-inner {
  width: 100%;
  height: 100%;
}
.carousel-pic,
.carousel-pic-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}
.carousel-bullets {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 52px;
  padding: 0 20px;
  text-align: center;
  box-sizing: border-box;
  &:last-child {
    bottom: 10px;
  }
}
.carousel-bullets-item {
  display: inline-block;
  padding: 0 6px;
  width: 22px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  border-radius: 200px;
  transition: opacity 0.3s ease;
  &:hover {
    opacity: 1;
    &::after {
      background-color: var(--carousel-bullets-color-hover, rgba(0, 0, 0, 0.5));
    }
  }
  &::after {
    content: '';
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 200px;
    background-color: var(--carousel-bullets-color-normal, rgba(0, 0, 0, 0.2));
    vertical-align: middle;
    transition: background-color 0.2s linear;
  }
  &.current,
  &.current:hover {
    &::after {
      background-color: var(--carousel-color-brand1);
    }
  }
}
.carousel-navigation-button-prev,
.carousel-navigation-button-next {
  width: 26px;
  height: 70px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  &::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 18px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-size: 100% auto;
    background-position: center center;
    background-repeat: no-repeat;
  }
  &:hover {
    background: rgba(0, 0, 0, 0.5);
  }
  &.disabled {
    cursor: default;
    opacity: 0.3;
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}
.carousel-navigation-button-prev {
  left: 20px;
  &::before {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC8AAAAvCAYAAABzJ5OsAAAABHNCSVQICAgIfAhkiAAAAjJJREFUaEPVmctKAzEUhg2IlyKIbyCoICgouHDpK7h3JXjBhSufwAewIipaXeiqL+DTuBAEQRAURHA9/qckMm3nkjM56ZwZCB3ak8z3nWQyydSMRTqSJFlB08coJ8aY3xiXMTEateA3aLuF8oxyEENAHH4A3OWGBI4g8COZLFH4HHDH+4KTfUkBMXiArwHu0g6VvASTwCEEviV6QATegl8DaMoD6hUxexICwfAAX7cZn/YAdyGngH9ixGeGBsEzM+4A2gDvhoJT/crwdYNXhtcAXgleCzgbXhM4Cx7gG6hw4Tkdit+cWTe41w1rwekBNMGYJcRmlbxrlsJrBS8dNprBC+G1g+fCNwE8E74p4EPwTQLvgwf4Jr441zYdFk3NvanSgtMDaFzTPF7GYpoK3hs2gD/D51aZZep32sLtYE3+wagTJZTgJ9EybeFoR+R7vCOQNtO1CrgxT3vPq6YJ/K9t0AMkcIuy6pt+xNXaA30LMwjQG647lGWmwC6G0Bejjkjo0KoSAjO2BzgCb6hDrzNGKpC5JIbArBVYYqSIBOgm/mTUCQrNXc9bgQ5aX2RcYaQ9ULgZ0S7gs5OiIaSyB0rhacho7QEveK0C3vAaBVjw2gTY8JoEKsFrEagMr0EgCD4lQIu5BcaTuP5/RhwsngNzOL9HmfcQEHuHGZx5pkAHCzeSFDnE4O0QKuqBR4DTbk3sEIUvEOgCvC1GbRsSh88QiAJO14kCnxLYRsYfpDPu2vsDznoAP/235QQAAAAASUVORK5CYII=);
  }
}
.carousel-navigation-button-next {
  right: 20px;
  &::before {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABHNCSVQICAgIfAhkiAAAAVhJREFUWEe9l0EKwjAQRc0pdC2eQhS9h6Ib9U7qRsGFOw8heAER1IWuFcEj1D+QihQzZiZTC6EtTfJfpj/TqasojyzLqhi6Qes75y7KaSpOM9CL7zC2jnZDa2ohxAAF8ZyfIDqAOEkXJAIIiOeaD1y0pBBSgB5EVswq7z4Sx9hIiABoUkRhjNOMERBFQgxgDaECsIRQA1hBJAF8QExxHZqL9UQygIcY4LzQQJgApECYAWghTAEEEF1kzAP1NweIhHiiX5sgSgHwEGTKIZMxr3jWKAUA6frXrigvAhJxcw9IxU0BID7ChHPmnVPYqWjZf/Yx8YBm5TlEMoCvD7hvwdtw36KTBBBRnLDiSR6IEKev4DvjhbyhikCkeFSBKgawFBe/Au/2JbPVyq2KAVCD+JZy+BcIUTWs3oYBiP/8GeXUBYj//hsWINa4nyC9nhlfsI/Eu0ArFBr3Al6mtyC1SAu/AAAAAElFTkSuQmCC);
  }
}

.carousel-thumbs {
  width: 100%;
  height: 42px;
  position: absolute;
  bottom: 0px;
  left: 0px;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
}
.carousel-thumbs-item {
  line-height: 42px;
  flex: 1 1 0%;
  text-align: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  position: relative;
  padding: 0px 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.25s ease;
  font-family: var(--font-family-system);
  &::before {
    content: '';
    width: 1px;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.2);
    position: absolute;
    right: 0px;
    top: 50%;
    margin-top: -10px;
  }
  &:last-child::before {
    display: none;
  }
  &.current {
    color: #fff;
    font-weight: 700;
  }
  &:hover {
    color: rgba(255, 255, 255, 0.7);
  }
}

.carousel-slide-words {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 10;
  font-size: 0.16rem;
}
