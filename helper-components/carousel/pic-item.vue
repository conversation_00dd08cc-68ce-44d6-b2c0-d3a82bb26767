<template>
  <div class="carousel-pic">
    <Transition name="image-transition">
      <img
        v-show="src"
        :alt="title"
        :src="src"
        :class="['carousel-pic-image', { error: isError }]"
        loading="lazy"
        @error="onError"
      />
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

defineProps<{
  index: number;
  title: string;
  src: string;
}>();

const isError = ref(false);

const onError = () => {
  isError.value = true;
};
</script>
