import { baseRequest, cookie } from '@tencent/wegame-web-sdk';

const BASE_FEEDS_API = '/api/forum/lua/wegame_feeds/';

const feedsListRequestHelper = async ({
  api,
  method = 'GET',
  data
}: {
  api: string;
  method?: string;
  data: any;
}) => {
  try {
    const results = await baseRequest({
      url: BASE_FEEDS_API + api,
      method,
      params:
        method.toLowerCase() === 'get'
          ? {
              p: JSON.stringify({
                ...(data || {}),
                uid: cookie.get('tgp_id') || '',
                _t: new Date().getTime() // IE会缓存get请求，加个时间戳
              })
            }
          : undefined,
      body:
        method.toLowerCase() === 'post'
          ? `p=${encodeURI(JSON.stringify(data || {}))}`
          : undefined,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    return results;
  } catch (err) {
    console.error('feeds api failedF', err);
  }
  return null;
};

export interface UserInfoItem {
  account_type: number;
  faceurl: string;
  nick: string;
  uid: string;
}

export const btGetUserInfo = async ({
  uid,
  dst_list
}: {
  uid: string;
  dst_list: string[];
}) => {
  const res = (await feedsListRequestHelper({
    api: 'bt_get_user_info',
    method: 'GET',
    data: {
      uid,
      dst_list,
      ext_info: 0
    }
  })) as { data: { result: number; info_list?: UserInfoItem[] } };
  if (res?.data?.result === 0) {
    return res.data.info_list || [];
  }
  return [];
};
