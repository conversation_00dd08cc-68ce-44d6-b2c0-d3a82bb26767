import { baseRequest } from '@tencent/wegame-web-sdk';

export const enum AccountType {
  unknown = 0,
  qq = 1,
  wx = 2
}

interface BaseRequestParams {
  game_id?: string;
  account_type?: AccountType;
  area?: number;
  from_src?: string;
  openid?: string;
}

export interface RoleInfoItem {
  openid: string;
  area: null;
  name: string;
  avatar: string;
}

export interface PerksCard {
  id: string;
  name: string;
  icon: string;
  desc: string;
}

// 任务状态
export enum TaskState {
  UNFINISHED,
  FINISHED,
  CLAIMED,
  UNCERTIFIED
}

export interface Reward {
  name: string;
  icon: string;
  count: number;
  level: number;
}

export interface TaskItem {
  id: string;
  name: string;
  desc: string;
  reward: Reward;
  state: TaskState;
}

export interface NewcomerReward {
  id: string;
  title: string;
  icon: string;
  desc: string;
}

/**
 * 封装基础请求逻辑
 * @param endpoint API端点
 * @param params 请求参数
 */
const fetchData = async <T>(
  endpoint: string,
  params: BaseRequestParams
): Promise<T | null> => {
  try {
    const response = await baseRequest<T>({
      url: `/api/v1/wegame.pallas.game.StudentActivity/${endpoint}`,
      method: 'POST',
      data: params
    });
    return response;
  } catch (error) {
    console.error(`${endpoint} failed:`, error);
  }
  return null;
};

/**
 * 查询账号id
 */
export const getAccount = async (
  params: BaseRequestParams
): Promise<string> => {
  const response = await fetchData<{ openid: string }>('GetAccount', params);
  return response?.openid ?? '';
};

/**
 * 查询用户是否已经通过高校认证
 */
export const getStudentCertifiedState = async ({
  from_src = ''
}: {
  from_src?: string;
}): Promise<{ certified: 0 | 1; school: string } | null> => {
  try {
    const response = await fetchData<{ certified: 0 | 1; school: string }>(
      'GetStudentCertifiedState',
      { from_src }
    );
   return response?.certified === 1 ? response : null;
  } catch (error) {
    console.error('GetStudentCertifiedState failed:', error);
  }
  return null;
};

/**
 * 查询角色信息
 */
export const getRoleInfo = async (
  params: BaseRequestParams
): Promise<RoleInfoItem | null> => {
  return (
    (await fetchData<{ role: RoleInfoItem }>('GetRoleInfo', params))?.role ||
    null
  );
};

/**
 * 获取高校特权列表
 */
export const getPerksList = async (
  params: BaseRequestParams
): Promise<PerksCard[]> => {
  const response = await fetchData<{ cards: PerksCard[] }>(
    'GetPerksList',
    params
  );
  return response?.cards || [];
};

/**
 * 获取每日任务
 */
export const getDailyTasks = async (
  params: BaseRequestParams
): Promise<TaskItem[]> => {
  const response = await fetchData<{ tasks: TaskItem[] }>(
    'GetDailyTasks',
    params
  );
  return response?.tasks || [];
};

/**
 * 获取邀请码
 */
export const getInvitationCode = async (
  params: BaseRequestParams
): Promise<string> => {
  const response = await fetchData<{ invitation_code: string }>(
    'GetInvitationCode',
    params
  );
  return response?.invitation_code ?? '';
};

/**
 * 获取邀请任务
 */
export const getInvitationTasks = async (
  params: BaseRequestParams
): Promise<TaskItem[]> => {
  const response = await fetchData<{ tasks: TaskItem[] }>(
    'GetInvitationTasks',
    params
  );
  return response?.tasks || [];
};

/**
 * 获取加码奖励任务
 */
export const getMoreRewardTasks = async (
  params: BaseRequestParams
): Promise<TaskItem[]> => {
  const response = await fetchData<{ tasks: TaskItem[] }>(
    'GetMoreRewardTasks',
    params
  );
  return response?.tasks || [];
};

/**
 * 领取加码任务奖励
 */
export const claimMoreReward = async (
  params: BaseRequestParams & { taskId: string }
): Promise<any> => {
  return fetchData('ClaimMoreReward', params);
};

/**
 * 获取新手奖励描述
 */
export const getNewcomerRewardDesc = async (
  params: BaseRequestParams
): Promise<NewcomerReward | null> => {
  const response = await fetchData<{ rewards: NewcomerReward }>(
    'GetNewcomerRewardDesc',
    params
  );
  return response?.rewards || null;
};

/**
 * 领取新手奖励
 */
export const claimNewcomerReward = async (
  params: BaseRequestParams & { rewardId: string; invitationCode: string }
): Promise<boolean> => {
  try {
    const response = await fetchData<{ result: { retCode: number } }>(
      'ClaimNewcomerReward',
      params
    );
    return response?.result?.retCode === 0;
  } catch (error) {
    console.error('ClaimNewcomerReward failed:', error);
  }
  return false;
};
