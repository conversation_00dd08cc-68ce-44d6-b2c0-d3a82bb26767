import { createGlobalState, useAsyncState } from '@vueuse/core';
import { unref, computed, MaybeRef, watch } from 'vue';
import {
  ACCOUNT_CHANNEL,
  INVALID_AREA_ID,
  useAccount
} from '../../composable/use-account';
import {
  getStudentCertifiedState,
  getPerksList,
  getDailyTasks,
  getAccount,
  getRoleInfo,
  getInvitationCode,
  getInvitationTasks,
  getMoreRewardTasks,
  getNewcomerRewardDesc
} from './api';

/**
 * 高校认证全局状态管理钩子
 *
 * 提供高校认证状态、特权列表和任务列表的统一管理
 *
 * @param {MaybeRef<string>} gameId - 游戏ID（响应式引用或字符串）
 * @returns {Object} 包含高校相关状态和方法的对象
 */
export const useCollegeWelfare = createGlobalState(
  (gameId: MaybeRef<string>) => {
    const curGameId = computed(() => unref(gameId));
    const fromSrc = computed(() => `helper_college_${curGameId.value}`);

    const { isLoadedArea, areaId, channel } = useAccount();

    const accountWithoutOpenid = computed(() => {
      if (areaId.value !== INVALID_AREA_ID) {
        return {
          game_id: curGameId.value,
          area: areaId.value,
          account_type: channel.value === ACCOUNT_CHANNEL.QQ ? 1 : 2,
          from_src: fromSrc.value
        };
      }
      return null;
    });

    const account = computed(() => {
      if (accountWithoutOpenid.value) {
        return {
          ...accountWithoutOpenid.value,
          openid: openid.value
        };
      }
      return null;
    });

    // 认证状态
    const {
      state: certifiedState,
      execute: executeCertifiedState,
      isLoading: isLoadingCertifiedState
    } = useAsyncState(
      async () => {
        return await getStudentCertifiedState({ from_src: fromSrc.value });
      },
      {
        certified: 0,
        school: ''
      },
      {
        resetOnExecute: false
      }
    );

    const {
      state: openid,
      execute: executeAccount,
      isLoading: isLoadingAccount
    } = useAsyncState(
      async () => {
        return await getAccount(accountWithoutOpenid.value!);
      },
      '',
      {
        immediate: false
      }
    );

    const { state: roleInfo, execute: executeRoleInfo } = useAsyncState(
      async () => {
        return await getRoleInfo(account.value!);
      },
      null,
      {
        immediate: false
      }
    );

    const { state: perksList, execute: executePerksList } = useAsyncState(
      async () => {
        return await getPerksList(account.value!);
      },
      [],
      {
        immediate: false
      }
    );

    const { state: dailyTasks, execute: executeDailyTasks } = useAsyncState(
      async () => {
        return await getDailyTasks(account.value!);
      },
      [],
      {
        immediate: false
      }
    );

    const { state: invitationCode, execute: executeInvitationCode } =
      useAsyncState(
        async () => {
          return await getInvitationCode(account.value!);
        },
        '',
        {
          immediate: false
        }
      );

    const { state: invitationTasks, execute: executeInvitationTasks } =
      useAsyncState(
        async () => {
          return await getInvitationTasks(account.value!);
        },
        [],
        {
          immediate: false
        }
      );

    const { state: moreRewardTasks, execute: executeMoreRewardTasks } =
      useAsyncState(
        async () => {
          return await getMoreRewardTasks(account.value!);
        },
        [],
        {
          immediate: false
        }
      );

    const { state: newcomerRewardDesc, execute: executeNewcomerRewardDesc } =
      useAsyncState(
        async () => {
          return await getNewcomerRewardDesc(account.value!);
        },
        null,
        {
          immediate: false
        }
      );

    watch(
      () => [isLoadedArea.value, areaId.value],
      () => {
        if (!isLoadedArea.value) return;
        if (areaId.value !== INVALID_AREA_ID) {
          executeAccount();
        }
      },
      { immediate: true }
    );

    watch(
      () => [isLoadingAccount.value, openid.value],
      () => {
        if (isLoadingAccount.value) return;
        if (openid.value) {
          executeRoleInfo();
          executePerksList();
          executeDailyTasks();
          executeInvitationCode();
          executeInvitationTasks();
          executeMoreRewardTasks();
          executeNewcomerRewardDesc();
        }
      }
    );

    return {
      isLoadingCertifiedState,
      certifiedState,
      roleInfo,
      perksList,
      dailyTasks,
      invitationCode,
      invitationTasks,
      moreRewardTasks,
      newcomerRewardDesc,
      checkStudentCertifiedState: executeCertifiedState
    };
  }
);
