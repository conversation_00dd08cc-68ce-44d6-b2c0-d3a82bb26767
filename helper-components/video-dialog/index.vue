<template>
  <Teleport to="body">
    <div ref="el" class="video-preview" :class="videoPlayerOpt?.parentClass">
      <div class="video-preview-modalbox">
        <div class="video-preview-modal">
          <!-- 关闭按钮 S  -->
          <span class="video-preview-close" @click="clickClose">
            <i class="video-preview-close-icon"></i>
          </span>
          <!-- 关闭按钮 E  -->

          <div class="video-preview-cont">
            <div class="video-preview-container">
              <!-- 视频播放区域 S  -->
              <VideoPlayer
                v-if="curVideoItem"
                v-model:play="isPlaying"
                :dom-id="`video-player-${uuid}`"
                :vid="curVideoItem.vid"
                :custom-class="videoPlayerOpt?.customClass"
                :off-screen-pause="videoPlayerOpt?.offScreenPause"
                :config="videoPlayerOpt?.config"
                :volume="videoPlayerOpt?.volume"
                @end="handleVideoEnd"
              />
              <!-- 视频播放区域 E  -->
            </div>

            <!-- 视频缩略图 S  -->
            <SlideMenu
              :slide-index="modelValue"
              :width="slideWidth"
              :loop="false"
              @update:slide-index="updateSlideIndex"
            >
              <SlideMenuItem
                v-for="(item, idx) in list"
                :key="idx"
                :app-id="appId"
                :item-index="idx"
              >
                <div class="video-thumb-card">
                  <div class="video-thumb-card-cover">
                    <img :src="item.cover" alt="" />
                  </div>
                  <div class="video-thumb-card-info">
                    <div class="video-thumb-card-title">
                      {{ item.title }}
                    </div>
                  </div>
                </div>
              </SlideMenuItem>
            </SlideMenu>
            <!-- 视频缩略图 E  -->
          </div>
        </div>
      </div>
      <div class="video-preview-mask"></div>
    </div>
  </Teleport>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';

import { ISuperPlayerConfig } from '@tencent/super-player';

import { generateUUID } from '../utils/uuid';
import { VideoItem } from '../video-list/interface';
import VideoPlayer from '../video-player';
import SlideMenu from './slide-menu.vue';
import SlideMenuItem from './slide-menu-item.vue';
import { useDocumentVisibility, useIntersectionObserver } from '@vueuse/core';

const emits = defineEmits<(e: 'close') => void>();

const props = withDefaults(
  defineProps<{
    appId: string;
    activeIndex: number;
    list: VideoItem[];
    slideWidth?: number;
    autoPlayNextVideo?: boolean;
    videoPlayerOpt?: {
      parentClass?: string | string[];
      customClass?: string | string[];
      offScreenPause?: boolean;
      config?: Omit<ISuperPlayerConfig, 'container'>;
      volume?: number;
    };
  }>(),
  {
    slideWidth: 680,
    autoPlayNextVideo: false,
    videoPlayerOpt: () => {
      return {};
    }
  }
);

const uuid = generateUUID();

const isPlaying = ref(true);
const el = ref<HTMLElement | null>(null);
const isInViewport = ref(false);
const modelValue = ref(props.activeIndex);

const documentVisibility = useDocumentVisibility();

useIntersectionObserver(
  el,
  ([{ isIntersecting }]) => {
    isInViewport.value = isIntersecting;
  },
  {
    threshold: [0]
  }
);

const curVideoItem = computed(() => {
  return props.list?.[modelValue.value];
});

const clickClose = () => {
  emits('close');
};

const updateSlideIndex = (index: number) => {
  modelValue.value = index;
};

const handleVideoEnd = () => {
  if (props?.autoPlayNextVideo) {
    updateSlideIndex((modelValue.value + 1) % props.list.length);
  }
};

const canPlay = computed(
  () => isInViewport.value && documentVisibility.value === 'visible'
);

watch(canPlay, val => {
  if (!val && isPlaying.value) {
    isPlaying.value = false;
  }
});
</script>
<style lang="scss">
@use './assets/scss/video-preview.scss';
</style>
