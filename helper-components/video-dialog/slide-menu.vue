<template>
  <div class="video-preview-thumbs">
    <div class="video-slider-list" :style="slideWrapStyle">
      <div
        ref="slideMenuInnerRef"
        class="video-slider-inner"
        :style="slideTransformStyle"
      >
        <slot></slot>
      </div>
    </div>
    <div class="video-slider-buttons">
      <div
        class="video-slider-button slider-button-prev"
        :class="buttonPrevClasses"
        @click="() => slideTo(props.slideIndex - 1)"
      ></div>
      <div
        class="video-slider-button slider-button-next"
        :class="buttonNextClasses"
        @click="() => slideTo(props.slideIndex + 1)"
      ></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, provide, onMounted, watch } from 'vue';

// 组件属性
const props = withDefaults(
  defineProps<{
    width: number;
    slideIndex?: number;
    loop?: boolean;
  }>(),
  {
    slideIndex: 0
  }
);

const emit =
  defineEmits<(event: 'update:slideIndex', slideIndex: number) => void>();

const slideMenus = ref<HTMLElement[]>([]);
const slideMenuInnerRef = ref<HTMLElement>();
const currentSlideIndex = ref<number>(props.slideIndex); // 内部slide-menu-item currentIndex维护

// 更新 current slide index
const updateSlideIndex = (index: number) => {
  emit('update:slideIndex', index);
};

provide('currentSlideIndex', currentSlideIndex);
provide('updateSlideIndex', updateSlideIndex);

const slideWrapStyle = computed(() => {
  return `width:${props.width}px`;
});

const getMenusWidth = (arr: HTMLElement[]) => {
  return arr.reduce((acc, currentValue) => acc + currentValue.offsetWidth, 0);
};

const currentTransform = computed(() => {
  const totalMove = getMenusWidth(slideMenus.value);
  const halfViewPortWidth = props.width / 2;
  if (totalMove < props.width) return 0;

  const currentMove = getMenusWidth(
    slideMenus.value.slice(0, props.slideIndex + 1)
  );

  // 判断最小滑动区域
  if (currentMove < halfViewPortWidth) {
    return 0;
  }

  // 判断最大滑动区域
  const moveMax = totalMove - halfViewPortWidth;
  if (currentMove > moveMax) {
    return (totalMove - props.width) * -1;
  }

  return (currentMove - halfViewPortWidth) * -1;
});

const slideTo = (index: number) => {
  let tempIndex;
  if (!props.loop) {
    tempIndex = Math.min(Math.max(0, index), slideMenus.value.length - 1);
  } else {
    // 负数求模 实现列表index头尾连接
    tempIndex = (index + slideMenus.value.length) % slideMenus.value.length;
  }
  updateSlideIndex(tempIndex);
};

// 内部组件数据更新
watch(
  () => props.slideIndex,
  () => {
    currentSlideIndex.value = props.slideIndex;
  }
);

const buttonPrevClasses = computed(() => {
  if (!props.loop) {
    return { disabled: props.slideIndex === 0 };
  }
  return '';
});
const buttonNextClasses = computed(() => {
  if (!props.loop) {
    return {
      disabled: props.slideIndex === slideMenus.value.length - 1
    };
  }
  return '';
});

const slideTransformStyle = computed(() => {
  return `transform:translateX(${currentTransform.value}px)`;
});

onMounted(() => {
  if (slideMenuInnerRef.value?.children) {
    slideMenus.value = Array.from(
      slideMenuInnerRef.value?.children
    ) as HTMLElement[];
  }
});
</script>

<style lang="scss"></style>
