<template>
  <div class="video-slider-item" :class="classes" @click="menuClick">
    <slot></slot>
  </div>
</template>
<script lang="ts" setup>
import { inject, computed, Ref } from 'vue';
import { reportEvent } from '../utils/report';

const props = defineProps<{
  appId: string;
  itemWidth?: number;
  itemIndex: number;
}>();
const currentSlideIndex = inject('currentSlideIndex') as Ref<number>;
const updateSlideIndex = inject('updateSlideIndex') as Function;

const menuClick = () => {
  updateSlideIndex(props.itemIndex);
  reportEvent({
    appId: props.appId,
    block: 'video_list',
    action: 'change_dialog_slider',
    ext: props.itemIndex
  });
};

const classes = computed(() => {
  return { current: currentSlideIndex.value === props.itemIndex };
});
</script>
