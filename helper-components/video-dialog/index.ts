import { h, render } from "vue";
import type { VNode } from "vue";
import type { ISuperPlayerConfig } from "@tencent/super-player";

import type { VideoItem } from "../video-list/interface";
import Dialog from "./index.vue";
import OneVideoDialog from "./index-one-video.vue";

type DialogOptions = {
  appId: string;
  list: VideoItem[];
  activeIndex: number;
  slideWidth?: number;
  autoPlayNextVideo?: boolean;
  videoPlayerOpt?: {
    parentClass?: string | string[];
    customClass?: string | string[];
    offScreenPause?: boolean;
    config?: Omit<ISuperPlayerConfig, "container">;
    volume?: number;
  };
};

export function show(options: DialogOptions) {
  const {
    appId,
    list,
    activeIndex,
    slideWidth,
    autoPlayNextVideo,
    videoPlayerOpt,
  } = options;
  const container = document.createElement("div");

  let instance: VNode | null = null;

  return new Promise((reject) => {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const DialogRootComponent = {
      render() {
        return h(Dialog, {
          appId,
          list,
          activeIndex,
          slideWidth,
          autoPlayNextVideo,
          videoPlayerOpt,
          onClose() {
            reject(null);
            destroy();
          },
        });
      },
    };
    document.body.appendChild(container);

    instance = h(DialogRootComponent);
    render(instance, container);

    function destroy() {
      render(null, container);
      document.body.removeChild(container);
      instance = null;
    }
  });
}

type OneVideoDialogOptions = {
  appId: string;
  url?: string;
  vid?: string;
  videoPlayerOpt?: {
    parentClass?: string | string[];
    customClass?: string | string[];
    offScreenPause?: boolean;
    config?: Omit<ISuperPlayerConfig, "container">;
    volume?: number;
  };
};

/**
 * 播放单一视频弹层
 * @param options
 * @returns
 */
export const showOneVideo = (options: OneVideoDialogOptions) => {
  const { appId, url, vid, videoPlayerOpt } = options;
  const container = document.createElement("div");

  let instance: VNode | null = null;

  return new Promise((reject) => {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const DialogRootComponent = {
      render() {
        return h(OneVideoDialog, {
          appId,
          url,
          vid,
          videoPlayerOpt,
          onClose() {
            reject(null);
            destroy();
          },
        });
      },
    };
    document.body.appendChild(container);

    instance = h(DialogRootComponent);
    render(instance, container);

    function destroy() {
      render(null, container);
      document.body.removeChild(container);
      instance = null;
    }
  });
};
