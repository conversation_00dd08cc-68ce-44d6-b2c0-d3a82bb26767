@use './video-thumbs.scss';
:root {
  --video-preview-color-fill: #3b4049;
}
.video-preview {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 300;
  overflow: auto;
  overflow-x: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  .txp_btn_pip,
  .txp_contextmenu {
    display: none !important;
  }
}
.video-preview-modal {
  width: 920px;
  position: relative;
  z-index: 10;
  background: var(--video-preview-color-fill);
  padding: 8px;
  box-sizing: content-box;
}
.video-preview-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
  background-color: rgba($color: #000000, $alpha: 0.5);
}

.video-preview-cont {
}
.video-preview-container {
  width: 100%;
  height: 520px;
  background-color: #000;
  video {
    width: 100%;
    height: 100%;
  }
}
.video-preview-thumbs {
  padding: 20px 60px 12px 60px;
  position: relative;
}
.video-preview-close {
  width: 48px;
  height: 48px;
  position: absolute;
  right: -48px;
  top: 0;
  background: var(--video-preview-color-fill);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    .video-preview-close-icon {
      opacity: 1;
    }
  }
}
.video-preview-close-icon {
  width: 24px;
  height: 24px;
  display: block;
  opacity: 0.6;
  transform: rotate(45deg);
  &::before,
  &::after {
    content: "";
    width: 16px;
    height: 2px;
    background-color: #000;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -1px 0 0 -8px;
  }
  &::after {
    width: 2px;
    height: 16px;
    margin: -8px 0 0 -1px;
  }
}

.video-preview-modal {
  animation: slideInModal 0.2s cubic-bezier(0, 0.62, 0.51, 0.93) 0s 1 both;
}
.video-preview-mask {
  animation: fadeInModalMask 0.2s linear 0s 1 both;
}
@keyframes slideInModal {
  0% {
    transform: translate3d(0, 30px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInModalMask {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.video-preview-modalbox {
  position: relative;
  z-index: 6;
}
.sidebar-on {
  .video-preview-modalbox {
    transform: translate3d(100px, 0, 0);
  }
}

.sidebar-off {
  .video-preview-modalbox {
    transform: translate3d(0, 0, 0);
  }
}

@media screen and (max-height: 820px) {
  body:not(.helper-v3) {
    .video-preview .video-preview-container {
      height: 380px;
    }
  }
}
