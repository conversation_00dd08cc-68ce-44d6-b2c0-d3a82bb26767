<template>
  <!-- props fill: 背景内容填充样式类型：
  默认：fill: cover,对应样式名: helper-video-bg fill-cover
  可选：fill: contain,对应样式名: helper-video-bg fill-contain; -->
  <div
    class="helper-video-bg"
    :class="[`fill-${props.fill}`, { 'show-blur-bg': isBlur }]"
  >
    <div class="helper-video-mask">
      <div class="helper-video-mask-top"></div>
      <div class="helper-video-mask-left"></div>
      <div class="helper-video-mask-right"></div>
      <div class="helper-video-mask-bottom"></div>
    </div>
    <div class="helper-video-ele">
      <!-- 视频 S -->
      <!-- <VideoPlayer
        dom-id="`helper-video-bg`"
        url="https://qa.wegame.gtimg.com/g.2001829-r.497bc/media_gen_7cdb6ae9c9eba68db781eabad0abd4d6.mp4"
      /> -->
      <video
        v-if="curVideoBg?.type === SHOW_TYPE.VIDEO && curVideoBg.url"
        :src="curVideoBg.url"
        loop
        autoplay
        muted
      ></video>
      <!-- 静态图 S -->
      <HelperImage
        v-else-if="hdImageURL"
        :src="hdImageURL"
        class="helper-video-img"
      />
    </div>
    <!-- 模糊图 -->
    <HelperImage
      v-if="blurImageURL"
      :src="blurImageURL"
      class="helper-video-blur-img"
    />
    <slot></slot>
  </div>
</template>
<script lang="ts" setup>
import './index.scss';
import HelperImage from '../helper-image/index.vue';
// import VideoPlayer from '../video-player/index.vue';
import { getVideoBg, VideoBgItem } from './api';
import { computed, watchEffect } from 'vue';
import { useAsyncState, useMediaQuery } from '@vueuse/core';
import { useGameState } from '../composable/use-game-state';

const props = withDefaults(
  defineProps<{
    fill?: 'cover' | 'contain';
    gameId: string;
    dataSource?: 'internal' | 'external';
    /**
     * 一般除首页外都要求背景模糊显示
     */
    isBlur?: boolean;
    videoBg?: VideoBgItem;
  }>(),
  {
    fill: 'cover',
    dataSource: 'internal',
    isBlur: false
  }
);

const SHOW_TYPE = {
  VIDEO: 'video',
  IMAGE: 'image'
};

const isLargeScreen = useMediaQuery('(min-width: 1920px)');

const { isRunning } = useGameState(props.gameId);

const { state, execute } = useAsyncState(getVideoBg(props.gameId), null, {
  immediate: false
});

const validVideoBg = computed(() => {
  if (props.dataSource === 'internal') {
    return state.value;
  }
  return props.videoBg;
});

/**
 * 可用视频
 */
const curVideo = computed(() => {
  const hdVideo =
    validVideoBg.value?.hd_video || validVideoBg.value?.video || '';
  const video = validVideoBg.value?.video || hdVideo || '';
  const url = isLargeScreen.value ? hdVideo : video;
  return { type: SHOW_TYPE.VIDEO, url };
});

/**
 * 高清图片
 */
const hdImageURL = computed(
  () => validVideoBg.value?.hd_pic || validVideoBg.value?.pic || ''
);

/**
 * 模糊图片
 */
const blurImageURL = computed(
  () => validVideoBg.value?.pic || validVideoBg.value?.hd_pic || ''
);

const curVideoBg = computed(() => {
  if (!isRunning.value && !props.isBlur && curVideo.value?.url) {
    return curVideo.value;
  }
  return null;
});

watchEffect(() => {
  if (props.dataSource === 'internal') {
    execute();
  }
});
</script>
