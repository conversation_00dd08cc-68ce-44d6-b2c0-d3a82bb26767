import { baseRequest } from '@tencent/wegame-web-sdk';

export interface VideoBgItem {
  hd_video?: string;
  video?: string;
  hd_pic?: string;
  pic?: string;
}

export const getVideoBg = async (gameId: string) => {
  try {
    const resp = await baseRequest<{ items: VideoBgItem[] }>({
      url: `/api/rail/web/data_filter/game_config/condition_search`,
      data: {
        data_names: 'helper_components_video_bg',
        search_pair: [{ key: 'game_id', value: gameId }],
        stamp: {}
      },
      method: 'POST'
    });
    return resp?.items?.[0];
  } catch (error) {
    console.error('`fetch helper_components_video_bg failed:', error);
  }
  return null;
};
