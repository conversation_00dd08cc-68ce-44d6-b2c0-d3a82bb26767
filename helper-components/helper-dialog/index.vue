<template>
  <template v-if="modelValue">
    <Teleport to="body">
      <div class="helper-dialog">
        <div class="helper-dialog-ele">
          <div v-if="withHeader" class="helper-dialog-header">
            <h3 class="helper-dialog-header-title">
              {{ popboxTitle }}
            </h3>
            <!-- 关闭按钮 S  -->
            <div class="helper-dialog-header-close" @click="handleCancel"></div>
            <!-- 关闭按钮 E  -->
          </div>

          <!-- 预设 弹框内容 S  -->
          <div v-if="!$slots.default" class="helper-dialog-cont">
            <span
              v-if="icon"
              :class="`helper-dialog-icon popbox-icon-${icon}`"
            ></span>
            <div v-if="title || desc" class="helper-dialog-main">
              <div v-if="title" class="helper-dialog-title">
                {{ title }}
              </div>
              <div v-if="desc" class="helper-dialog-desc" v-html="desc"></div>
            </div>
            <div v-if="$slots.main" class="helper-dialog-main-custom">
              <slot name="main"></slot>
            </div>
          </div>
          <!-- 预设 弹框内容 E  -->

          <!-- 自定义 框体内容 插槽 S -->
          <div v-if="$slots.default" class="helper-dialog-cont-custom">
            <slot></slot>
          </div>
          <!-- 自定义 框体内容 插槽 E -->

          <!-- 预设 footer内容 S  -->
          <div v-if="withFooter" class="helper-dialog-footer">
            <div class="helper-dialog-buttons">
              <div class="helper-dialog-button primary" @click="handleConfirm">
                <span class="helper-dialog-button-text">确定</span>
                <div class="helper-dialog-button-bg"></div>
              </div>
              <div class="helper-dialog-button normal" @click="handleCancel">
                <span class="helper-dialog-button-text">取消</span>
                <div class="helper-dialog-button-bg"></div>
              </div>
            </div>
          </div>
          <!-- 预设 footer内容 E  -->

          <!-- 自定义 footer 插槽 S -->
          <div v-if="$slots.footer" class="helper-dialog-footer-custom">
            <slot name="footer"></slot>
          </div>
          <!-- 自定义 footer 插槽 E -->
        </div>
        <div class="helper-dialog-mask"></div>
      </div>
    </Teleport>
  </template>
</template>

<script setup lang="ts">
import './index.scss';
const emits = defineEmits(['update:modelValue', 'confirm', 'cancel']);

defineProps<{
  modelValue: boolean;
  popboxTitle?: string;
  title?: string;
  desc?: string;
  icon?: 'info' | 'success' | 'error';
  withHeader?: boolean;
  withFooter?: boolean;
  withClose?: boolean;
}>();

const handleConfirm = () => {
  emits('update:modelValue', false);
  emits('confirm');
};

const handleCancel = () => {
  emits('update:modelValue', false);
  emits('cancel');
};
</script>
