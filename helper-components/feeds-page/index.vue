<template>
  <div v-if="isWeGame" class="feeds-page">
    <iframe
      v-show="isLoaded"
      ref="iframe"
      frameborder="0"
      framespacing="0"
      allowtransparency="true"
      title="bbs"
      :src="src"
      class="iframe-bbs"
    ></iframe>
  </div>
  <div v-else class="feeds-page nodata">
    <div class="nodata-deco"></div>
    <div class="nodata-text">游戏动态请在 WeGame 客户端内观看</div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { isWeGame } from '@tencent/wegame-web-sdk';
import { addStyleToFeedsPage } from '../utils/iframe';
import baseCssFileStyle from './assets/bbs-patch-base.css?inline';

const emits = defineEmits<(e: 'complete') => void>();

const props = defineProps<{
  appId: string;
  src: string;
  cssFileStyle: string;
  colorMode?: 'light' | 'dark';
  /**
   * iframe加载完回调函数
   */
  callback?: Function;
}>();

const iframe = ref<HTMLIFrameElement>();

const isLoaded = ref(false);

const initIFrame = async () => {
  if (iframe.value) {
    try {
      await addStyleToFeedsPage({
        iframe: iframe.value,
        cssFileStyle: props.cssFileStyle
          ? props.cssFileStyle
          : baseCssFileStyle,
        colorMode: props.colorMode,
        callback: props.callback
      });
      emits('complete');
    } catch (error) {}
    isLoaded.value = true;
  }
};

const postMessageToIFrame = <T,>(params: T) => {
  if (!iframe.value) return;
  iframe.value.contentWindow!.postMessage(
    JSON.stringify(params),
    location.origin
  );
};

onMounted(() => {
  initIFrame();
});

defineExpose({ postMessageToIFrame });
</script>

<style lang="scss">
@use './assets/scss/feeds-page.scss';
</style>
