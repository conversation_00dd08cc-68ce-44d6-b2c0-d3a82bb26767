import { baseRequest } from '@tencent/wegame-web-sdk';

import type { LiveItem, LiveLabelType } from './interface';

export async function getLiveLabelList(
  appId: string
): Promise<LiveLabelType[]> {
  try {
    const resp = (await baseRequest({
      url: '//www.wegame.com.cn/api/qt/lua/tgp_live_svr/get_second_game_type',
      params: {
        p: {
          game_id: appId
        }
      }
    })) as {
      data: {
        label_list: LiveLabelType[];
      };
    };
    if (resp?.data?.label_list) {
      return resp.data.label_list;
    }
    throw new Error(JSON.stringify(resp));
  } catch (error) {
    console.error(`getLiveLabelList failed:${error}`);
  }
  return [];
}

/**
 * 获取某游戏某个label下的直播列表
 * @param labelId get_second_game_type接口返回的label_id字段
 */
export async function getCateLiveList({
  appId,
  labelId,
  pageSize = 6
}: {
  appId: string;
  labelId: string;
  pageSize?: number;
}): Promise<LiveItem[]> {
  try {
    const resp = (await baseRequest({
      url: '//www.wegame.com.cn/api/forum/lua/mwg_live_svr/get_recommend_live_list',
      data: {
        count: pageSize,
        offset: 0,
        pc_homepage: appId,
        sec_tab_id: labelId,
        tab_id: `3_${appId}` // 1_1这种是拉取全部游戏下的推荐直播列表  3_{GAME_ID}这种是拉取具体游戏下的直播列表
      },
      method: 'POST',
      cacheable: true,
      cacheKey: labelId
    })) as {
      code: number;
      data: {
        lists: LiveItem[];
      };
    };
    if (resp?.code === 0) {
      return resp.data.lists;
    }
    throw new Error(JSON.stringify(resp));
  } catch (error) {
    console.error(`fetch cate live list failed:${error}`);
  }
  return [];
}

/**
 * 获取某游戏下的所有直播
 */
export async function getAllLiveList({
  appId,
  pageSize = 6
}: {
  appId: string;
  pageSize?: number;
}): Promise<LiveItem[]> {
  const params = {
    url: `//www.wegame.com.cn/api/qt/lua/tgp_live_svr/get_lives_list?p=${JSON.stringify(
      {
        gid: appId,
        page: 1,
        perpage: pageSize,
        pc_homepage: 1
      }
    )}`
  };

  try {
    const resp = (await baseRequest({
      url: params.url,
      method: 'POST',
      cacheable: true,
      cacheKey: 'allLive'
    })) as {
      data: {
        lists: LiveItem[];
      };
    };
    if (resp?.data.lists) {
      return resp.data.lists;
    }
    throw new Error(JSON.stringify(resp));
  } catch (error) {
    console.error(`fetch all live list failed:${error}`);
  }
  return [];
}
