<template>
  <li class="live-list-item">
    <!-- 直播卡片 S  -->
    <div class="live-card" @click="openLive(item.live_id)">
      <!-- 直播卡片封面区域 S  -->
      <div class="live-card-cover">
        <div class="live-card-image">
          <!-- 直播卡片封面图 S  -->
          <img class="live-card-img" alt="" :src="item.room_pic" />
          <!-- 直播卡片封面图 E  -->
        </div>

        <!-- 直播平台 S  -->
        <!-- 来源参考LOL: huya | doyu | egame(电竞) | huomao(火猫) | lz | panda | qm | yy | zhanqi -->
        <span
          v-if="livePlatform"
          :class="['live-card-tag', livePlatform.class]"
        >
          {{ livePlatform.title }}
        </span>
        <!-- 直播平台 E  -->

        <!-- 直播平台人数 S  -->
        <div v-if="item.user_num" class="live-card-online">
          <span>{{ item.user_num }}</span>
          <span></span>
        </div>
        <!-- 直播平台人数 E  -->
      </div>
      <!-- 直播卡片封面区域 E  -->

      <!-- 直播摘要信息 S  -->
      <div class="live-card-info">
        <div class="live-card-title">
          <!-- 标题  -->
          <span class="live-card-title-text"> {{ item.room_name }} </span>
        </div>
        <div class="live-card-meta">
          <span class="live-card-author-avatar">
            <img class="live-card-author-image" alt="" :src="item.owner_pic" />
          </span>
          <!-- 来源 -->
          <span class="live-card-author"> {{ item.owner_name }} </span>

          <!-- 时间 -->
          <span class="live-card-time"> </span>
        </div>
      </div>
      <!-- 直播摘要信息 E  -->
    </div>
    <!-- 直播卡片 E  -->
  </li>
</template>
<script lang="ts" setup>
import { tgp } from '@tencent/wegame-web-sdk';
import { computed } from 'vue';
import { reportEvent } from '../utils/report';
import { getLivePlatform } from './const';

import { LiveItem } from './interface';

const props = defineProps<{
  appId: string;
  item: LiveItem;
}>();

const livePlatform = computed(() => {
  return getLivePlatform(props.item.live_type);
});

// 点击直播item打开直播页面
const openLive = (liveId: number) => {
  const query = `media_type=live&gid=${props.appId}&media_id=${liveId}&from_type=helper`;
  const url = `https://www.wegame.com.cn/platform/social/videoLiveGame?${query}`;
  tgp.get({
    app: 0,
    cmd: 'open_inner_link',
    data: {
      type: 'open_social',
      url
    },
    fail() {
      window.open(url, '_blank');
    }
  });
  reportEvent({
    appId: props.appId,
    block: 'live_item',
    action: 'open_live'
  });
};
</script>
