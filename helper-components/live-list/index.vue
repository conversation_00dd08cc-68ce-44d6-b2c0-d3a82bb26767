<template>
  <div
    v-if="isShow"
    ref="container"
    :data-appid="appId"
    data-block="liveList"
    class="helper-panel helper-panel--live"
  >
    <div class="helper-panel-main">
      <!-- 面板容器 头部 S -->
      <div class="helper-panel-header">
        <div class="helper-panel-title">
          <strong class="helper-panel-title-text">{{ title }}</strong>
        </div>
        <!-- 面板容器 工具栏 S -->
        <div class="helper-panel-tools">
          <!-- <slot name="tools"></slot> -->
          <ul class="panel-tools-tab">
            <li
              v-for="(label, idx) in liveLabelList"
              :key="label.label_id"
              class="panel-tools-tab-menu"
              :class="{ current: idx === activeLabelIndex }"
              @click="clickLabel(label, idx)"
            >
              <a href="javascript:;" class="panel-tools-tab-item">
                {{ label.label_name }}
              </a>
            </li>
          </ul>
        </div>
        <!-- 面板容器 工具栏 E -->
      </div>
      <!-- 面板容器 头部 E -->
      <div class="helper-panel-cont">
        <!-- 列表 S -->
        <div :class="['live-list', listClass]">
          <ul class="live-list-inner">
            <!-- 直播列表项 S  -->
            <LiveItem
              v-for="item in liveList"
              :key="`${item.live_type}_${item.live_id}`"
              :app-id="appId"
              :item="item"
            />

            <!-- 直播列表项 E  -->
          </ul>
        </div>
        <!-- 列表 E -->
      </div>
    </div>
    <!-- 面板背景 S  -->
    <div class="helper-panel-bg"></div>
    <!-- 面板背景 E  -->
  </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { useThrottleFn } from '@vueuse/core';

import { getAllLiveList, getCateLiveList, getLiveLabelList } from './api';
import { LiveLabelType, LiveItem as ILiveItem } from './interface';
import LiveItem from './live-item.vue';
import { reportExposure } from '../utils/report';

const props = withDefaults(
  defineProps<{
    appId: string;
    title?: string;
    rows?: number; // x行
    cols?: number; // x列
  }>(),
  {
    title: '热门直播',
    rows: 2,
    cols: 3
  }
);

const emits = defineEmits<(e: 'hide') => void>();

const container = ref(null);
const liveLabelList = ref<LiveLabelType[]>([]);
const activeLabelIndex = ref(0);
const liveList = ref<ILiveItem[]>([]);

const pageSize = computed(() => props.rows * props.cols);

const isShow = computed(() => {
  // 全部视频大于3才显示直播列表
  return !(activeLabelIndex.value === 0 && liveList.value.length < 3);
});

const listClass = computed(() => {
  if (props.cols === 2) {
    return 'live-list--cols-2';
  }
  return 'live-list--cols-3';
});

const fetchData = async () => {
  const [liveListRes, labelListRes] = await Promise.all([
    getAllLiveList({ appId: props.appId, pageSize: pageSize.value }),
    getLiveLabelList(props.appId)
  ]);
  liveList.value = [...liveListRes];
  liveLabelList.value = labelListRes.slice(0, 10);
  if (!isShow.value) {
    emits('hide');
  }
};

const clickLabel = useThrottleFn(async (item: LiveLabelType, idx: number) => {
  if (activeLabelIndex.value === idx) {
    return;
  }

  liveList.value = [];
  activeLabelIndex.value = idx;
  if (item.label_name === '全部') {
    liveList.value = await getAllLiveList({
      appId: props.appId,
      pageSize: pageSize.value
    });
  } else {
    liveList.value = await getCateLiveList({
      appId: props.appId,
      labelId: item.label_id,
      pageSize: pageSize.value
    });
  }
}, 500);

watch(
  () => isShow.value,
  async val => {
    if (val) {
      await nextTick();
      container.value && reportExposure([container.value]);
    }
  },
  {
    immediate: true
  }
);

onMounted(() => {
  fetchData();
});
</script>

<style lang="scss">
@use './assets/scss/live-list.scss';
</style>
