<template>
  <div class="popbox-preview" :class="[show ? 'popbox-show' : 'popbox-hide']">
    <div class="popbox-preview-inner">
      <div class="popbox-preview-hd">
        <a
          class="popbox-preview-close"
          href="javascript:void(0);"
          @click="clickClose"
        ></a>
      </div>
      <div class="popbox-preview-bd">
        <div class="gallerythumb-wrapper-cont">
          <TransitionGroup name="gallerythumblist">
            <div
              v-for="item in wallpaperInPreviewList"
              v-show="wallpaper.file_id === item.file_id"
              :key="item.file_id"
              class="gallerythumb-wrapper"
            >
              <div class="gallerythumb-banner-list">
                <div class="gallerythumb-banner-item">
                  <div
                    class="gallerythumb-banner-poster"
                    :style="`background: ${item.theme_color}`"
                  >
                    <img
                      :key="item.file_id"
                      alt="wallpaper"
                      :src="item.preview || item.url"
                      :width="item.width"
                      :height="item.height"
                    />
                  </div>
                  <div class="gallerythumb-banner-info">
                    <div class="gallerythumb-banner-title">
                      {{ item.title }}
                    </div>
                    <div class="gallery-thumbcard-intro-wrap">
                      <div class="gallery-thumbcard-intro">
                        <span
                          class="gallery-thumbcard-intro-icon status-heatnum"
                        >
                        </span>
                        <span class="gallery-thumbcard-intro-text">
                          {{ item.popularity }}
                        </span>
                      </div>
                      <div class="gallery-thumbcard-intro">
                        <span
                          class="gallery-thumbcard-intro-icon status-picturesize"
                        >
                        </span>
                        <span class="gallery-thumbcard-intro-text">
                          {{ item.width }}x{{ item.height }}
                        </span>
                      </div>
                      <div v-if="fileSize" class="gallery-thumbcard-intro">
                        <span
                          class="gallery-thumbcard-intro-icon status-filesize"
                        ></span>
                        <span class="gallery-thumbcard-intro-text">
                          {{ fileSize }}
                        </span>
                      </div>
                    </div>
                    <div class="gallerythumb-banner-btnwrap">
                      <div
                        v-if="isWeGameMain && showSetAsWallpaper"
                        :class="[
                          'gallerythumb-banner-btn',
                          { disabled: savingWallpaper }
                        ]"
                        @click="handleSetAsWallpaper(item)"
                      >
                        <span class="gallerythumb-banner-icon"> </span>
                        <span class="gallerythumb-banner-btn-text">
                          {{ savingWallpaper ? '设置中' : '设置为壁纸' }}
                        </span>
                      </div>
                      <div
                        v-if="showDelete"
                        :class="[
                          'gallerythumb-banner-btn',
                          { disabled: deleting }
                        ]"
                        @click.stop="handleDelete(item)"
                      >
                        <span class="gallerythumb-banner-icon"> </span>
                        <span class="gallerythumb-banner-btn-text">
                          {{ deleting ? '删除中' : '删除截图' }}
                        </span>
                      </div>
                      <div
                        v-if="showDownload"
                        :class="[
                          'gallerythumb-banner-btn',
                          { disabled: downloading }
                        ]"
                        @click.stop="handleDownload(item)"
                      >
                        <span class="gallerythumb-banner-icon"> </span>
                        <span class="gallerythumb-banner-btn-text">
                          {{ downloading ? '下载中' : '下载至本地' }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TransitionGroup>
        </div>
        <div class="gallerythumb-banner-navigation">
          <div
            v-if="curIndex > 0"
            class="gallerythumb-banner-navigation-btn prev"
            :class="{ disabled: processingWallpaper }"
            @click="clickPrev"
          ></div>
          <div
            v-if="curIndex < wallpaperList.length - 1"
            class="gallerythumb-banner-navigation-btn next"
            :class="{ disabled: processingWallpaper }"
            @click="clickNext"
          ></div>
        </div>
      </div>
    </div>
    <div class="popbox-preview-mask"></div>
  </div>
</template>
<script lang="ts" setup>
import './assets/wallpaper-preview.scss';
import { computed, ref, watch } from 'vue';

import { WgToast } from '@tencent/wg-ui';
import { isWeGameMain } from '@tencent/wegame-web-sdk';

import { likeWallpaper, type WallpaperItem } from './wallpaper-model';
import { setWallpaper, downloadImage } from './wallpaper-utils';
import { reportEvent } from '../utils/report';

const emits = defineEmits(['update:show', 'next', 'prev', 'delete']);

const props = withDefaults(
  defineProps<{
    gameId: string;
    show: boolean;
    curIndex: number;
    wallpaperList: Array<WallpaperItem>;
    likeEnabled?: boolean;
    showSetAsWallpaper?: boolean;
    showDownload?: boolean;
    showDelete?: boolean;
  }>(),
  {
    showSetAsWallpaper: false,
    showDownload: false,
    likeEnabled: false,
    showDelete: false
  }
);

const wallpaper = computed(() => props.wallpaperList[props.curIndex]);

const lastPreviewWallpaper = ref<WallpaperItem | null>(null);

const downloading = ref(false);

const savingWallpaper = ref(false);

const deleting = ref(false);

const clickClose = () => {
  emits('update:show', false);
};

watch(
  () => wallpaper.value,
  (_, oldVal) => {
    if (!oldVal) {
      return;
    }
    if (oldVal.file_id !== lastPreviewWallpaper.value?.file_id) {
      lastPreviewWallpaper.value = oldVal;
    } else {
      lastPreviewWallpaper.value = null;
    }
  }
);

const wallpaperInPreviewList = computed(
  () =>
    [wallpaper.value, lastPreviewWallpaper.value].filter(
      Boolean
    ) as WallpaperItem[]
);

const fileSize = computed(() => {
  if (!wallpaper.value?.size) {
    return '';
  }

  if (wallpaper.value.size < 1024) {
    return `${wallpaper.value.size}B`;
  }

  if (wallpaper.value.size < 1024 * 1024) {
    return `${(wallpaper.value.size / 1024).toFixed(1)}KB`;
  }
  return `${(wallpaper.value.size / 1024 / 1024).toFixed(1)}MB`;
});

const processingWallpaper = computed(
  () => downloading.value || savingWallpaper.value
);

const handleSetAsWallpaper = (item: WallpaperItem) => {
  const wallpaperStyle = item.width <= item.height ? 3 : 4;
  savingWallpaper.value = true;
  setWallpaper(item.url, wallpaperStyle)
    .then(() => {
      WgToast.success('设置壁纸成功');
      props.likeEnabled &&
        likeWallpaper({ gameId: props.gameId, fileId: item.file_id });
    })
    .finally(() => {
      savingWallpaper.value = false;
    });
  reportEvent({
    block: 'audiovisual_wallpaper_preview',
    action: 'set_wallpaper',
    ext: item.title
  });
};

const handleDownload = (item: WallpaperItem) => {
  downloading.value = true;
  downloadImage(item.url, item.title, 'jpg')
    .then(() => {
      isWeGameMain && WgToast.success('下载成功');
      props.likeEnabled &&
        likeWallpaper({ gameId: props.gameId, fileId: item.file_id });
    })
    .finally(() => {
      downloading.value = false;
    });
  reportEvent({
    block: 'audiovisual_wallpaper_preview',
    action: 'download_wallpaper',
    ext: item.title
  });
};

const handleDelete = (item: WallpaperItem) => {
  emits('delete', item);
};

const clickNext = () => {
  if (processingWallpaper.value) {
    return;
  }
  emits('next');
  reportEvent({
    block: 'audiovisual_wallpaper_preview',
    action: 'next'
  });
};

const clickPrev = () => {
  if (processingWallpaper.value) {
    return;
  }
  emits('prev');
  reportEvent({
    block: 'audiovisual_wallpaper_preview',
    action: 'prev'
  });
};
</script>
