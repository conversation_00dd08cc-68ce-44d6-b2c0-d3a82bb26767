<script is:inline>
  const statusSidebarCSS = {
    0: 'sidebar-hidden', // 单launcher完全收起客户端左侧
    1: 'sidebar-on', // 展开客户端左侧
    2: 'sidebar-off' // 收起客户端左侧
  };
  const isWeGame =
    typeof window !== 'undefined' && /WeGame\//.test(navigator.userAgent);
  if (!isWeGame) {
    document.body.classList.add('page-out-client');
  } else {
    // inline提前处理侧边栏
    const setSideBarClass = status => {
      if (statusSidebarCSS[status]) {
        document.body.classList.remove(
          'sidebar-on',
          'sidebar-off',
          'sidebar-hidden'
        );
        document.body.classList.add(statusSidebarCSS[status]);
      }
    };
    const status =
      typeof top !== 'undefined' && top.__CLIENTINFO__
        ? top?.__CLIENTINFO__?.sideBarStatus
        : 1;
    setSideBarClass(status);
    const result = navigator.userAgent.match(/WeGame\/([^ ]+)/);
    if (result?.[1]) {
      const version = result[1].split('.')?.[0];
      if (Number(version) >= 6) {
        document.body.classList.add('helper-v3');
      }
    }
    const handleSideBarEvent = e => {
      if (e.data && e.data.cmd !== 'switch_side_bar') {
        return;
      }
      const status = e.data.type;
      setSideBarClass(status);
    };
    window.addEventListener('message', handleSideBarEvent);
  }
</script>
