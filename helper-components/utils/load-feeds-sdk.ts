import { loadjs } from '@tencent/wegame-web-sdk';
import { loadFeedsManifest } from './load-feeds-manifest';

let sdkLoadPromise: ReturnType<typeof loadFeedsSdk> | null = null;

export const loadFeedsSdk = async (): Promise<void> => {
  const manifest = await loadFeedsManifest();

  const feedConfig = manifest['feeds-sdk']?.default;
  if (!feedConfig) {
    throw new Error('manifest must provide feeds-sdk');
  }

  if (sdkLoadPromise) {
    return sdkLoadPromise;
  }
  sdkLoadPromise = loadjs(
    [
      '//wegame.gtimg.com/g.55555-r.c4663/lib/jquery/2.2.2/jquery.min.js',
      '//wegame.gtimg.com/g.55555-r.c4663/lib/vue/2.6.10/vue.min.js',
      feedConfig.script,
      feedConfig.style
    ],
    {
      returnPromise: true,
      async: false
    }
  );

  sdkLoadPromise?.catch(error => {
    console.error('load feeds sdk failed:', error);
  });

  return sdkLoadPromise;
};

declare global {
  interface Window {
      Feeds: {
          popup: (options: { feedsId: string }) => void;
      };
  }
}