import { tgp, isWeGame } from '@tencent/wegame-web-sdk';
import { setUrlParameter } from './utils';

export const GAME_ID_SHOP = 50000013; // 商店
// export const GAME_ID_HOME_PAGE = 50000014; // 个人首页
// export const GAME_ID_NETBAR = 50000015; // 网吧专区
// export const GAME_ID_TESTZONE = 50000019; // 测试专区
// export const GAME_ID_WALLPAPER = 2001068; // 壁纸工坊

export interface JumpItem {
  jump_url: string;
  jump_url_type?: 'system' | 'new_wegame_window' | 'direct'; // 系统浏览器 | WeGame浏览器 | 直接跳转
  jump_appid?: number | string; // 跳转类型直接跳转才有效，50000013商店|50000014个人主页|50000015网吧专区|50000016社区首页|50000019测试专区|2001068壁纸工坊
  jump_withPtlogin?: '1' | '0'; // 跳转链接类型为WeGame浏览器才有效
}

/**
 *
 * @param param0
 * @param param0.jump_url 跳转URL
 * @param param0.jump_url_type 跳转链接类型
 * @param param0.jump_appid 跳转appid
 * @param param0.jump_withPtlogin 跳转链接类型为WeGame浏览器才有效
 */
export const jumpPage = ({
  jump_url,
  jump_url_type,
  jump_appid,
  jump_withPtlogin
}: JumpItem) => {
  const url = setUrlParameter(jump_url, {
    hcfrom: 'WeGame.helper'
  });
  switch (jump_url_type) {
    case 'new_wegame_window':
      if (isWeGame) {
        tgp.open({
          url,
          legacyPtLogin: jump_withPtlogin === '1'
        });
      } else {
        window.open(url, '_blank');
      }
      break;
    case 'direct':
      {
        const opt: {
          page: string;
          app: string | number;
        } = {
          page: url,
          app: 0
        };
        if (isWeGame && jump_appid && Number(jump_appid) !== 0) {
          opt.app = jump_appid;
          tgp.jump(opt);
        } else {
          location.href = opt.page;
        }
      }
      break;
    default:
      window.open(url, '_blank');
      break;
  }
};
