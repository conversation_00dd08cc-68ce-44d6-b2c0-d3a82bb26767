import type { Plugin } from "vue";

export type SFCWithInstall<T> = T & Plugin;

export const withInstall = <
  T,
  D extends Record<string, any>,
  E extends Record<string, any>,
>(
  main: T,
  directives?: D,
  extra?: E,
) => {
  (main as SFCWithInstall<T>).install = (app: any): void => {
    for (const comp of [main, ...Object.values(extra ?? {})]) {
      app.component(comp.name, comp);
    }
    if (directives) {
      for (const [key, directive] of Object.entries(directives)) {
        app.directive(key, directive);
      }
    }
  };

  if (extra) {
    for (const [key, comp] of Object.entries(extra)) {
      (main as any)[key] = comp;
    }
  }

  return main as SFCWithInstall<T> & E;
};
