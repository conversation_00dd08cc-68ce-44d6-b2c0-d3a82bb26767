import { init, report } from '@tencent/wegame-web-sdk';

type EventOptions = Parameters<ReturnType<typeof init>>[2];

/**
 * 通用的执行上报的方法
 * @param params 上报时携带的参数信息，包括pageName 页面名称、block 模块名等
 * @param eventOptions 额外指定配置
 * @param eventOptions.immediate 实时上报
 * @param eventOptions.sendBeacon 离开页面时上报，在上报后页面会跳转时需指定为true
 * @param eventOptions.realTimeAnalysis 实时上报到TDM
 */
export const reportEvent = (
  params: {
    block: string;
    action: string;
    [x: string]: any;
  },
  eventOptions: EventOptions = {}
) => {
  const {
    immediate = false,
    sendBeacon = false,
    realTimeAnalysis = false
  } = eventOptions;

  const name = 'helper_components';
  report?.reportEvent(name, params, {
    immediate,
    sendBeacon,
    realTimeAnalysis
  });
};

type ExposureCallback = (element: Element) => void;

const ExposeThreshold = 0.4;

const createExposureObserver = (
  exposureCallback: ExposureCallback
): IntersectionObserver => {
  const reportedElements = new Set<Element>();

  return new IntersectionObserver(
    (entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !reportedElements.has(entry.target)) {
          exposureCallback(entry.target);
          reportedElements.add(entry.target);
          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: ExposeThreshold }
  );
};

const handleExposureReporting = (
  elements: Element[],
  observer: IntersectionObserver
): void => {
  elements.forEach(element => observer.observe(element));
};

const handleReportExposure = (element: Element) => {
  const { appid, block } = (element as HTMLElement).dataset;
  if (!appid || !block) return;
  reportEvent({
    appId: appid,
    block,
    action: 'expose'
  });
};

const reportedReferCountElements = new Set<Element>();

const reportReferCount = (element: Element) => {
  if (reportedReferCountElements.has(element)) return;
  reportedReferCountElements.add(element);
  const { appid, block } = (element as HTMLElement).dataset;
  if (!appid || !block) return;
  reportEvent({
    appId: appid,
    block,
    action: 'reportRefer'
  });
};

/**
 * 模块曝光，只上报1次 上报元素需要appid跟block数据，例如 data-appid="12345" data-block="videoList"
 * @param elements
 */
let exposureObserver: null | IntersectionObserver = null;
export const reportExposure = (elements: Element[]) => {
  if (!exposureObserver) {
    exposureObserver = createExposureObserver(handleReportExposure);
  }
  elements.forEach(element => reportReferCount(element));
  handleExposureReporting(elements, exposureObserver);
};
