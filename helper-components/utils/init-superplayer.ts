import loadjs from 'loadjs';
import SuperPlayer from '@tencent/super-player';
import type { ISuperPlayerConfig } from '@tencent/super-player';
import { getBusinessConfig } from '../video-player/const';

let superPlayerLoadPromise: undefined | Promise<any> = undefined;

export function loadSuperPlayerSdk() {
  if (superPlayerLoadPromise) {
    return superPlayerLoadPromise;
  }
  superPlayerLoadPromise = loadjs(
    ['//vm.gtimg.cn/thumbplayer/superplayer/superplayer.js'],
    {
      returnPromise: true
    }
  );

  superPlayerLoadPromise!
    .then(() => {
      return loadjs(
        ['//vm.gtimg.cn/thumbplayer/core/latest/txhlsjs-kernel.js'],
        {
          returnPromise: true
        }
      );
    })
    .catch(err => {
      console.error('load superplayer sdk failed:', err);
    });

  return superPlayerLoadPromise;
}

const defaultBusinessConfig = getBusinessConfig('default');

export const initPlayer = async (
  opts: ISuperPlayerConfig
): Promise<SuperPlayer> => {
  await loadSuperPlayerSdk();
  const defaultConfig: ISuperPlayerConfig = {
    ...opts,
    // 传入需要展示播放器的容器选择器
    container: opts.container,
    businessConfig: opts.businessConfig || defaultBusinessConfig,
    // @ts-ignore
    autoPlayPolicy: 'autoPlayInMuted',
    ctrlConfig: {
      disableFakeFullscreen: true
    },
    removeComponents: [
      'ui-watermark', // 移除水印
      ...(opts.removeComponents || [])
    ]
  };

  const player = new window.SuperPlayer({
    ...defaultConfig
  });
  player.volume = 0;

  return player;
};
