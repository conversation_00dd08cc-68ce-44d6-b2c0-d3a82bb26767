import jsCookie from 'js-cookie';
import { getLocalStorageItem, setLocalStorageItem } from './storage';

const getKey = (key: string, gameId: string) => {
  const tgpId = jsCookie.get('tgp_id');
  return `Helper${key}_${gameId || 0}_${tgpId}`;
};

export const getNewIconRead = (key: string, gameId: string) => {
  const localData = getLocalStorageItem(getKey(key, gameId));
  return localData === '1';
};

export const setNewIcon = (key: string, gameId: string) => {
  setLocalStorageItem(getKey(key, gameId), '1');
};
