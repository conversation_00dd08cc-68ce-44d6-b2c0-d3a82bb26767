export const enum WsType {
  /** 修改心跳时间间隔 */
  Pong = 'pong',
  /** 修改订阅房间时间间隔 */
  SubRoomRsp = 'subroomRsp',
  /** 创建房间事件 */
  Create = 'createRoom',
  /** 修改房间事件 */
  Modify = 'modifyRoom',
  /** 成员进出房间事件 */
  Inout = 'inoutRoom',
  /** 解散房间事件 */
  Dissolve = 'dissolveRoom',
  /** 成员变动事件 */
  MemberChange = 'memberChange'
}

/**
 * 重连最大次数
 */
const reconnectMax = 10;
export class WebSocketClient {
  private url: string;
  private messageCallback: (message: string) => void;
  private websocket: WebSocket | null = null;
  private reconnectInterval: number;
  private heartbeatInterval: number;
  private reconnectTimeout: ReturnType<typeof setTimeout> | null = null;
  private heartbeatTimeout: ReturnType<typeof setTimeout> | null = null;
  private reconnectCount: number;

  constructor(
    url: string,
    messageCallback: (message: string) => void,
    reconnectInterval = 3000,
    heartbeatInterval = 10000
  ) {
    this.url = url;
    this.messageCallback = messageCallback;
    this.reconnectInterval = reconnectInterval;
    this.heartbeatInterval = heartbeatInterval;
    this.reconnectCount = 0;
    this.connect();
  }

  testCMD() {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(
        JSON.stringify({ cmd: 'test_close', data: { abc: 1 } })
      );
      // console.log('WebSocket sent test_close');
    }
  }

  setBeatInterval(val: number) {
    this.heartbeatInterval = val;
    this.startHeartbeat();
  }

  /**
   * 订阅房间（最后需额外添加大区id，后台用以确定广播对象）
   * @param room_ids
   */
  subRoom(room_ids: string[]) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({ cmd: 'subroom', room_ids }));
      // console.log('WebSocket sent subRoom: ', room_ids);
    }
  }

  disconnect() {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    this.stopHeartbeat();
  }

  private connect() {
    this.websocket = new WebSocket(this.url);

    this.websocket.onopen = () => {
      // console.log('WebSocket connected');
      this.startHeartbeat();
    };

    this.websocket.onmessage = event => {
      // console.log('WebSocket message received:', event.data);
      this.messageCallback(event.data);
    };

    this.websocket.onclose = () => {
      // console.log('WebSocket disconnected');
      this.stopHeartbeat();
      this.scheduleReconnect();
    };

    this.websocket.onerror = error => {
      console.error('WebSocket error:', error);
    };
  }

  private scheduleReconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }
    this.reconnectTimeout = setTimeout(() => {
      // console.log('Reconnecting WebSocket...');
      this.reconnectCount += 1;
      if (this.reconnectCount < reconnectMax) {
        this.connect();
      }
    }, this.reconnectInterval);
  }

  private startHeartbeat() {
    this.stopHeartbeat();
    this.heartbeatTimeout = setInterval(() => {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify({ cmd: 'ping' }));
        // console.log('WebSocket sent heartbeat');
      }
    }, this.heartbeatInterval);
  }

  private stopHeartbeat() {
    if (this.heartbeatTimeout) {
      clearInterval(this.heartbeatTimeout);
    }
  }
}
