/**
 * 定制样式feeds页
 * @param param0
 * @returns
 */
export const addStyleToFeedsPage = ({
  iframe,
  cssFileStyle,
  colorMode,
  callback
}: {
  iframe: HTMLIFrameElement;
  cssFileStyle: string;
  colorMode?: 'light' | 'dark';
  callback?: Function;
}) => {
  return new Promise((resolve, reject) => {
    iframe.addEventListener('load', () => {
      const bbsContentDocument = iframe.contentWindow;
      if (bbsContentDocument) {
        const bbsDocument = bbsContentDocument.document;
        bbsDocument.body.classList.add('body-bbs-page');
        const htmlClassList = bbsDocument.querySelector('html')?.classList;
        if (htmlClassList) {
          // 部分游戏需要支持浅色模式，例如仙境传说
          if (colorMode === 'light') {
            htmlClassList.remove('theme-cn-dark');
            htmlClassList.add('theme-cn-light');
          } else {
            htmlClassList.remove('theme-cn-light');
            htmlClassList.add('theme-cn-dark');
          }
        }
        const style = bbsDocument.createElement('style');
        style.setAttribute('type', 'text/css');
        style.innerHTML = cssFileStyle;
        bbsDocument.head.appendChild(style);
        iframe.classList.add('show');
        if (callback && typeof callback === 'function') {
          callback();
        }
        resolve(true);
      } else {
        reject(false);
      }
    });
    iframe.addEventListener('error', () => {
      reject(false);
    });
  });
};
