import { baseRequest } from '@tencent/wegame-web-sdk';

/**
 * 性别: 0男,1女,其他未知
 */
export const enum Gender {
  Unknown = -1,
  Male = 0,
  Female = 1
}

export interface TeamUpUserConfig {
  /**
   * 是否展示性别
   */
  show_gender: 1 | 0;
}

/**
 * 开黑玩家信息，跟Member不一样，User可以设置偏好
 */
export interface TeamUpUser {
  admin: boolean;
  /**
   * 是否被管理员拉黑
   */
  ban: boolean;
  /**
   * 解禁日期，需要先判断是否被拉黑
   */
  ban_time: number;
  ban_enter_room_time: number;
  config: TeamUpUserConfig;
  gender: Gender;
  /**
   * WeGame头像
   **/
  icon: string;
  preference: {
    create_room: {
      room_name: string;
      mode: number;
      max_val: number;
      min_val: number;
    };
    get_room: { key: string; operator: '='; value: string | number }[];
  };
  /**
   * 开黑场次
   */
  total: number;
  /**
   * 开黑胜利场次
   */
  win: number;
}

export interface TeamUpMember {
  tgpid: string;
  openid: string;
  roleid: string;
  area: number;
  name: string;
  /** WeGame 头像 */
  icon: string;
  gender: Gender;
  total: number;
  win: number;
}

export const enum TeamUpRoomState {
  Unknown = 0,
  /**
   * 组队中
   */
  Access = 1,
  /**
   * 满员
   */
  Full = 2,
  /**
   * 组队完成
   */
  Finish = 3,
  /**
   * 已解散（前端定义，除已开启外的所有需要隐藏的房间）
   */
  Dissolved = 99
}

export interface TeamUpRoomInfo {
  room_info: {
    /** 部分接口会有这个字段 */
    area?: number;
    room_id: string;
    room_name: string;
    mode: number;
    min_val: number;
    max_val: number;
    /** 隐私 0: 没有隐私 1： 设置隐私 */
    privacy: 0 | 1;
    state: TeamUpRoomState; // 状态
    owner: string;
  };
  members: TeamUpMember[];
}

export const getTeamUpUserInfo = async ({
  teamUpAppId,
  areaId,
  fromSrc
}: {
  teamUpAppId: string;
  areaId?: number;
  fromSrc?: string;
}) => {
  try {
    const resp = await baseRequest<{
      user: TeamUpUser;
    }>({
      url: '/api/v1/wegame.pallas.game.TeamUp/GetUserInfo',
      data: {
        app_id: teamUpAppId,
        area: areaId,
        from_src: fromSrc
      },
      method: 'POST',
      headers: {
        'trpc-caller': 'wegame.pallas.game.TeamUp' // 方便后台监控
      }
    });

    if (resp.user) {
      // 填写默认数据
      resp.user.config = resp.user.config || {
        show_gender: 1
      };
      const preference = resp.user.preference || {};
      preference.create_room = preference.create_room || {};
      preference.get_room = preference.get_room || [];
      resp.user.preference = preference;
    }
    return resp.user;
  } catch (error) {
    console.error('getUserInfo error', error);
    return null;
  }
};

export interface RoomFilter {
  key: string;
  operator: '=' | '>=' | '>' | '<=' | '<' | 'in' | 'not in';
  value: string | number;
}

export const formatRoomMember = (member: TeamUpMember) => {
  const gender = member.gender ?? 0;
  return {
    ...member,
    gender
  };
};

/**
 * 获取房间列表
 * @param param0
 */
export const getRoomList = async ({
  teamUpAppId,
  areaId,
  filters,
  fromSrc
}: {
  teamUpAppId: string;
  areaId: number;
  filters: RoomFilter[];
  fromSrc?: string;
}) => {
  try {
    const resp = await baseRequest<{ rooms: TeamUpRoomInfo[] }>({
      url: '/api/v1/wegame.pallas.game.TeamUp/GetRoomList',
      data: {
        app_id: teamUpAppId,
        area: areaId,
        filters,
        from_src: fromSrc
      },
      method: 'POST',
      headers: {
        'trpc-caller': 'wegame.pallas.game.TeamUp' // 方便后台监控
      }
    });
    const rooms = (resp?.rooms || []).map(item => {
      const members = item.members.map(member => formatRoomMember(member));
      return {
        ...item,
        members
      };
    });
    return {
      rooms
    };
  } catch (error) {
    console.error('getRoomList error', error);
  }
  return {
    rooms: []
  };
};
