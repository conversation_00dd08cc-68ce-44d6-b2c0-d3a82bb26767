<template>
  <div class="audio-ctrl-wrapper">
    <div
      :class="['audio-ctrl-icon', isPlaying && 'audio-ctrl-icon--play']"
      @click="toggleAudio"
    ></div>
    <div class="audio-ctrl-infobox">
      <div v-if="!isError" class="audio-ctrl-info">
        <div class="audio-tit">
          {{ audio.title }}
        </div>
        <div class="audio-time">
          {{ audioTimeText }}
        </div>
      </div>
      <div v-if="isError && errorTxt" class="audio-ctrl-err">
        {{ errorTxt }}
      </div>
      <div v-show="dragging" class="audio-progress">
        <div class="audio-progress-line">
          <div
            class="audio-progress-bg"
            :style="`width: ${Math.floor(dragProgress * 100)}%`"
          >
          </div>
        </div>
        <div
          class="audio-progress-icon"
          :style="`left: calc(${Math.floor(dragProgress * 100)}% - 0.08rem)`"
        >
        </div>
      </div>
      <div v-show="!dragging" class="audio-progress">
        <div
          ref="scrollbarBg"
          class="audio-progress-line"
          @click="clickScrollbarBg"
        >
          <div
            class="audio-progress-bg"
            :style="`width: ${Math.floor(progress * 100)}%`"
          >
          </div>
        </div>
        <div
          ref="scrollbarThumb"
          class="audio-progress-icon"
          :style="`left: calc(${Math.floor(progress * 100)}% - 0.08rem)`"
          @mousedown="handleMousedownBar"
        >
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, ref } from 'vue';
import { useThrottleFn } from '@vueuse/core';
import { AudioItem } from './interface';
import { reportEvent } from '../utils/report';

const emits = defineEmits<{
  (e: 'pause'): void;
  (e: 'play', value: { audio: AudioItem }): void;
  (e: 'set-current-time', value: { audio: AudioItem; time: number }): void;
  (e: 'update:globalMuted', value: boolean): void;
}>();

const props = withDefaults(
  defineProps<{
    /**
     * 只用于上报
     */
    appId?: string;
    globalMuted?: boolean;
    audio: AudioItem;
    audioTimeText: string;
    isPlaying: boolean;
    isError: boolean;
    errorTxt?: string;
    currentTime?: number;
    duration: number;
    /**
     * 数据上报到ext字段
     */
    reportExt?: string;
  }>(),
  {
    appId: '',
    globalMuted: false,
    currentTime: 0,
    isError: false,
    errorTxt: '播放错误，请先下载/检查后重试~',
    reportExt: ''
  }
);

const toggleAudio = () => {
  reportEvent({
    appId: props.appId,
    block: 'audio_ctrl_bar',
    action: props.isPlaying ? 'pause' : 'play',
    ext: props.reportExt
  });
  if (props.isPlaying) {
    emits('pause');
  } else {
    emits('update:globalMuted', false);
    emits('play', { audio: props.audio });
  }
};

const progress = computed(() => {
  return props.currentTime / (props.duration || 1);
});

const dragging = ref(false);
let dragStartX = 0;
let scrollbarX = 0;
let scrollbarWidth = 1;
const scrollbarBg = ref<HTMLElement | null>(null);
const scrollbarThumb = ref<HTMLElement | null>(null);
const dragProgress = ref(0);

const handleMousedownBar = (event: MouseEvent) => {
  dragProgress.value = progress.value;
  dragging.value = true;
  dragStartX = event.clientX;
  const bgRect = scrollbarBg.value!.getBoundingClientRect();
  scrollbarX = bgRect.x;
  scrollbarWidth = bgRect.width;
  document.addEventListener('mousemove', handleMousemove);
  document.addEventListener('mouseup', handleMouseup);
};

const handleMousemove = useThrottleFn((event: MouseEvent) => {
  const diff = event.clientX - dragStartX;
  let newThumbX = diff + dragStartX - scrollbarX;
  newThumbX = Math.max(0, newThumbX);
  newThumbX = Math.min(scrollbarWidth, newThumbX);

  dragProgress.value = newThumbX / scrollbarWidth;
}, 10);

const setCurrentTime = async (time: number) => {
  emits('update:globalMuted', false);
  await nextTick();
  emits('set-current-time', { audio: props.audio, time });
};

const handleMouseup = async () => {
  document.removeEventListener('mousemove', handleMousemove);
  document.removeEventListener('mouseup', handleMouseup);
  dragging.value = false;
  const time = Math.floor(dragProgress.value * props.duration);
  setCurrentTime(time);
};

const clickScrollbarBg = useThrottleFn((event: MouseEvent) => {
  const bgRect = scrollbarBg.value!.getBoundingClientRect();
  scrollbarX = bgRect.x;
  scrollbarWidth = bgRect.width;
  const progress = (event.clientX - scrollbarX) / scrollbarWidth;
  const time = Math.floor(progress * props.duration);
  setCurrentTime(time);
}, 500);
</script>

<style lang="scss">
@use './assets/scss/audio-ctrl-bar.scss';
</style>
