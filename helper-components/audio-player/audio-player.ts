import { isBrowser } from '@tencent/wegame-web-sdk';

/**
 * 播放模式
 */
export const enum PlayModeKey {
  /**
   * 顺序播放
   */
  SequentialPlayback,
  /**
   * 随机播放
   */
  RandomPlayback,
  /**
   * 单曲循环
   */
  SingleTrackRepeat,
  /**
   * 列表循环
   */
  PlaylistRepeat
}

export class AudioPlayer implements EventTarget {
  private url = '';
  private audioElement: HTMLAudioElement = new Audio();
  private eventTarget: EventTarget = new EventTarget();
  private progressEvent: CustomEvent = new CustomEvent('progress', {
    detail: {
      currentTime: 0,
      duration: 0,
      progress: 0
    }
  });
  private errorEvent: CustomEvent = new CustomEvent('error', {
    detail: {
      code: 0,
      message: ''
    }
  });
  private playEvent: Event = new Event('play');
  private pauseEvent: Event = new Event('pause');
  private endedEvent: Event = new Event('ended');
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private isPlaying = false;
  private targetVolume = 1;
  /**
   * 播放暂停声音行为（淡入淡出|立刻生效）
   */
  private behavior: 'smooth' | 'instant' = 'smooth';
  private fadeInTimer: ReturnType<typeof setTimeout> | null = null;
  private fadeOutTimer: ReturnType<typeof setTimeout> | null = null;

  constructor(opt?: { volume: number; behavior?: 'smooth' | 'instant' }) {
    if (!isBrowser) return;
    this.url = '';
    this.audioElement = new Audio();
    this.audioElement.style.display = 'none';
    this.audioElement.muted = true; // 将音频设置为静音
    this.audioElement.autoplay = true;
    document.body.appendChild(this.audioElement);
    if (opt?.volume) {
      this.targetVolume = Math.max(0, Math.min(1, opt.volume));
    } else {
      this.targetVolume = 1;
    }
    this.setVolume(this.targetVolume);
    this.behavior = opt?.behavior ?? 'smooth';
    this.fadeInTimer = null;
    this.fadeOutTimer = null;

    this.isPlaying = false;

    this.audioElement.addEventListener('play', this.handlePlay);
    this.audioElement.addEventListener('pause', this.handlePause);
    this.audioElement.addEventListener('timeupdate', this.handleTimeupdate);
    this.audioElement.addEventListener('ended', this.handleEnded);
    this.audioElement.addEventListener('error', this.handleError);
  }

  play(): void {
    this.audioElement.muted = false;
    if (this.behavior === 'smooth') {
      this.fadeIn();
    } else {
      this.audioElement.volume = this.targetVolume;
    }
    this.audioElement.play();
  }

  /**
   * 支持手动点击时立刻生效，否则感觉卡了
   * @param params
   */
  pause(params?: { behavior: 'smooth' | 'instant' }): void {
    // 可以在整体淡入淡出时立刻暂停，切换助手淡出，点击暂停按钮立刻
    if (this.behavior === 'smooth' && params?.behavior !== 'instant') {
      this.fadeOut();
    } else {
      this.audioElement.pause();
    }
  }

  resume(): void {
    this.audioElement.muted = false;
    if (this.behavior === 'smooth') {
      this.fadeIn();
    } else {
      this.audioElement.volume = this.targetVolume;
    }
    this.setCurrentTime(this.progressEvent.detail.currentTime);
    this.audioElement.play();
  }

  changeSource(url: string): void {
    if (!isBrowser) return;
    this.url = url;
    if (this.audioElement.src !== url) {
      this.audioElement.src = url;
    }
    this.play();
  }

  setVolume(volume: number): void {
    this.targetVolume = Math.max(0, Math.min(1, volume));
    this.audioElement.volume = volume;
  }

  setCurrentTime(time: number): void {
    // 少于2s以内的时间不重置，否则会一直
    if (Math.abs(this.audioElement.currentTime - time) > 2) {
      this.audioElement.currentTime = time;
    }
  }

  destroy(): void {
    this.audioElement.removeEventListener('play', this.handlePlay);
    this.audioElement.removeEventListener('pause', this.handlePause);
    this.audioElement.removeEventListener('timeupdate', this.handleTimeupdate);
    this.audioElement.removeEventListener('ended', this.handleEnded);
    this.audioElement.removeEventListener('error', this.handleError);
    document.body.removeChild(this.audioElement);
  }

  getUrl(): string {
    return this.url;
  }

  getIsPlaying(): boolean {
    return this.isPlaying;
  }

  onProgress(): void {
    const { currentTime } = this.audioElement;
    const { duration } = this.audioElement;
    const progress = (currentTime / duration) * 100;

    this.progressEvent.detail.currentTime = currentTime;
    this.progressEvent.detail.duration = duration;
    this.progressEvent.detail.progress = progress;

    this.dispatchEvent(this.progressEvent);
  }

  addEventListener(
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions
  ): void {
    this.eventTarget?.addEventListener(type, listener, options);
  }

  removeEventListener(
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | EventListenerOptions
  ): void {
    this.eventTarget?.removeEventListener(type, listener, options);
  }

  dispatchEvent(event: Event): boolean {
    return this.eventTarget.dispatchEvent(event);
  }

  private fadeIn(): void {
    this.fadeOutTimer && clearTimeout(this.fadeOutTimer);
    if (this.audioElement.volume < 1) {
      this.audioElement.volume = Math.min(1, this.audioElement.volume + 0.01);
      this.fadeInTimer = setTimeout(() => {
        this.fadeIn();
      }, 10);
    }
  }

  private fadeOut(): void {
    this.fadeInTimer && clearTimeout(this.fadeInTimer);
    if (this.audioElement.volume > 0) {
      this.audioElement.volume = Math.max(0, this.audioElement.volume - 0.01);
      this.fadeOutTimer = setTimeout(() => {
        this.fadeOut();
      }, 10);
    } else {
      this.audioElement.pause();
    }
  }

  private handlePlay = () => {
    this.isPlaying = true;
    this.dispatchEvent(this.playEvent);
  };

  private handlePause = () => {
    this.isPlaying = false;
    this.dispatchEvent(this.pauseEvent);
  };

  private handleTimeupdate = () => {
    this.onProgress();
  };

  private handleEnded = () => {
    this.isPlaying = false;
    this.dispatchEvent(this.endedEvent);
  };

  private handleError = () => {
    this.errorEvent.detail.code = this.audioElement.error?.code;
    this.errorEvent.detail.message = this.audioElement.error?.message;
    this.dispatchEvent(this.errorEvent);
  };
}
