:root {
  --laytou-left-width: 1.6rem;
  --laytou-right-width: 1rem;
}

html {
  font-size: calc(100vw / 1600) * 100;
  font-family: var(--helper-font-family-system);
}
@media screen and (min-aspect-ratio: 16/9) and (max-height: 1300px) {
  html {
    font-size: calc(100vh / 900) * 100 !important;
  }
}

.page-frame {
  height: 100vh;
  position: absolute;
  top: 0;
  left: var(--helper-sidebar-on-width);
  right: 0;
  z-index: 50;
  transition: var(--transition-left-navbar);
}

.page-frame-wrap {
  width: 100vw;
  height: 100vh;
  position: relative;
}
.page-section {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
}

.page-section-main {
  height: 100%;
  position: relative;
  z-index: 9;
  margin: 0 auto;
}

.page-layout-left {
  width: var(--laytou-left-width);
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  pointer-events: none;
}
.page-layout-right {
  width: var(--laytou-right-width);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
}

.page-layout-left-main,
.page-layout-right-main {
  position: relative;
  height: 100%;
  flex: 1;
}
.page-layout-tools {
  position: relative;
}
.page-layout-main {
  margin: 0 var(--laytou-right-width) 0 var(--laytou-left-width);
  position: relative;
  height: 100%;

  &:last-child {
    margin-right: 0;
  }
  &:first-child {
    margin-left: 0;
  }
}

.app-container {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 10;
}
.app-container-bg {
  position: fixed;
  inset: 0;
  z-index: 1;
  pointer-events: none;
}

body:not(.page-out-client) {
  .page-layout-left,
  .page-layout-right {
    top: var(--helper-navigation-height);
  }
  .page-layout-main {
    padding-top: var(--helper-navigation-height);
  }
}

// 路由切换动画
.router-enter-active {
  transition: opacity 0.6s;
}
.router-leave-active {
  opacity: 0;
  transition: opacity 0s;
}
.router-enter-from,
.router-leave-to {
  opacity: 0;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@media screen and (min-aspect-ratio: 16/9) and (max-height: 1300px) {
  html {
    font-size: calc(100vh / 900) * 100 !important;
  }
}
