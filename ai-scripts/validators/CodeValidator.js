/**
 * 代码验证器
 * 提供多层验证：语法检查、路径检查、导入检查
 */

import fs from 'fs'

class CodeValidator {
  constructor() {
    this.errors = []
  }

  /**
   * 验证Vue文件代码
   */
  async validateVueCode(vueCode, componentName) {
    this.errors = []
    
    try {
      // 1. 语法检查
      await this.validateSyntax(vueCode)
      
      // 2. 导入路径检查
      await this.validateImports(vueCode)
      
      // 3. 基础结构检查
      await this.validateStructure(vueCode)
      
      if (this.errors.length === 0) {
        console.log(`✅ ${componentName} 验证通过`)
        return true
      } else {
        throw new Error(this.errors.join('; '))
      }
      
    } catch (error) {
      console.log(`❌ ${componentName} 验证失败: ${error.message}`)
      throw error
    }
  }

  /**
   * Vue语法检查
   */
  async validateSyntax(vueCode) {
    try {
      // 检查是否有Vue编译器
      let parse
      try {
        const compiler = await import('@vue/compiler-sfc')
        parse = compiler.parse
      } catch (e) {
        console.log('⚠️  @vue/compiler-sfc 未安装，跳过语法检查')
        return true
      }

      const { descriptor, errors } = parse(vueCode)
      
      if (errors && errors.length > 0) {
        this.errors.push(`语法错误: ${errors.map(e => e.message).join(', ')}`)
        return false
      }

      // 检查必需的部分
      if (!descriptor.template) {
        this.errors.push('缺少 template 部分')
        return false
      }

      if (!descriptor.script && !descriptor.scriptSetup) {
        this.errors.push('缺少 script 部分')
        return false
      }

      return true
      
    } catch (error) {
      this.errors.push(`语法检查失败: ${error.message}`)
      return false
    }
  }

  /**
   * 导入路径检查
   */
  async validateImports(vueCode) {
    try {
      // 提取所有import语句
      const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g
      const imports = [...vueCode.matchAll(importRegex)]
      
      for (const match of imports) {
        const importPath = match[1]

        // 跳过别名导入检查（helper-components等），因为它们由构建工具处理
        if (importPath.startsWith('helper-components/')) {
          continue
        }
        // 检查相对路径导入
        else if (importPath.startsWith('./') || importPath.startsWith('../')) {
          if (!fs.existsSync(importPath)) {
            this.errors.push(`导入路径不存在: ${importPath}`)
          }
        }
        // Vue相关导入跳过检查
        else if (importPath === 'vue' || importPath.startsWith('@vue/')) {
          continue
        }
        // 其他第三方包跳过检查
        else {
          continue
        }
      }
      
      return this.errors.length === 0
      
    } catch (error) {
      this.errors.push(`导入检查失败: ${error.message}`)
      return false
    }
  }

  /**
   * 基础结构检查
   */
  async validateStructure(vueCode) {
    try {
      // 检查是否包含基本的Vue结构
      if (!vueCode.includes('<template>')) {
        this.errors.push('缺少 <template> 标签')
      }

      if (!vueCode.includes('<script setup>') && !vueCode.includes('<script>')) {
        this.errors.push('缺少 <script> 标签')
      }

      // 检查template中是否有内容
      const templateMatch = vueCode.match(/<template>([\s\S]*?)<\/template>/)
      if (templateMatch) {
        const templateContent = templateMatch[1].trim()
        if (!templateContent || templateContent === '<div>\n    <!-- AI 在这里生成组件使用代码 -->\n  </div>') {
          this.errors.push('template 内容为空或未被替换')
        }
      }

      // 检查script中是否有导入
      const scriptMatch = vueCode.match(/<script setup>([\s\S]*?)<\/script>/)
      if (scriptMatch) {
        const scriptContent = scriptMatch[1].trim()
        if (!scriptContent.includes('import') || scriptContent === "import { ref } from 'vue'\n// AI 在这里生成导入语句和逻辑代码") {
          this.errors.push('script 内容为空或未被替换')
        }
      }

      return this.errors.length === 0
      
    } catch (error) {
      this.errors.push(`结构检查失败: ${error.message}`)
      return false
    }
  }

  /**
   * 验证markdown文档
   */
  async validateMarkdown(mdContent, demoFiles) {
    this.errors = []
    
    try {
      // 检查demo引用路径
      const demoRefs = mdContent.match(/<demo src="([^"]+)"/g) || []
      
      for (const ref of demoRefs) {
        const pathMatch = ref.match(/src="([^"]+)"/)
        if (pathMatch) {
          const demoPath = pathMatch[1]
          
          // 检查对应的demo文件是否存在
          const found = demoFiles.some(demo => 
            demoPath.endsWith(`${demo.type}.vue`)
          )
          
          if (!found) {
            this.errors.push(`markdown中引用的demo文件不存在: ${demoPath}`)
          }
        }
      }

      // 检查基本结构
      if (!mdContent.includes('# ')) {
        this.errors.push('markdown缺少主标题')
      }

      if (!mdContent.includes('## API')) {
        this.errors.push('markdown缺少API文档部分')
      }

      return this.errors.length === 0
      
    } catch (error) {
      this.errors.push(`markdown验证失败: ${error.message}`)
      return false
    }
  }

  /**
   * 获取所有错误信息
   */
  getErrors() {
    return this.errors
  }

  /**
   * 清除错误信息
   */
  clearErrors() {
    this.errors = []
  }
}

export default CodeValidator
