/**
 * AI文档生成器
 * 生成完整的ElementUI风格组件文档
 */

import AIService from '../services/AIService.js'
import config from '../config/ai-config.js'
import fs from 'fs'

class MarkdownDocGenerator {
  constructor() {
    this.aiService = new AIService(config)
  }

  /**
   * 生成完整的组件文档
   */
  async generateCompleteDoc(componentPath) {
    console.log(`📝 生成完整文档: ${componentPath}`)

    try {
      // 1. 读取组件源码
      const sourceCode = fs.readFileSync(componentPath, 'utf8')
      
      // 2. 推断组件信息
      const componentInfo = this.inferComponentInfo(componentPath)

      // 3. 读取参考文档
      const referenceDoc = await this.loadReferenceDoc()
      
      // 4. 构建AI提示词
      const prompt = this.buildDocPrompt(sourceCode, componentInfo,referenceDoc)
      
      // 5. AI生成文档
      const markdownContent = await this.aiService.callAI(prompt)
      
      console.log(`✅ ${componentInfo.name} 文档生成成功`)
      return this.cleanupMarkdown(markdownContent)
      
    } catch (error) {
      console.error(`❌ 文档生成失败:`, error.message)
      throw error
    }
  }

  /**
   * 从组件路径推断组件信息
   */
  inferComponentInfo(componentPath) {
    // 获取组件名
    const pathParts = componentPath.split('/')
    const dirName = pathParts[pathParts.length - 2]
    const componentName = this.toPascalCase(dirName)
    
    // 生成导入路径
    const importPath = componentPath.replace(/^\.\//, '').replace('/index.vue', '')
    
    return {
      name: componentName,
      dirName: dirName,
      importPath: importPath,
      filePath: componentPath
    }
  }

  /**
   * 加载参考文档示例
   */
  async loadReferenceDoc() {
    try {
      // 读取现有的高质量文档作为参考
      const helperRadioDoc = fs.readFileSync('./docs/components/helper-radio.md', 'utf8')
      
      return {
        helperRadio: helperRadioDoc
      }
    } catch (error) {
      console.warn('无法加载参考文档，使用默认模板')
      return null
    }
  }

  /**
   * 构建文档生成的AI提示词
   */
  buildDocPrompt(sourceCode, componentInfo,referenceDoc) {
    return `你是一个前端组件库文档生成专家。请分析Vue组件源码，生成标准的组件使用文档。

组件源码：
\`\`\`vue
${sourceCode}
\`\`\`

请严格按照以下标准格式生成文档：

# ${componentInfo.name} 组件

（写明组件的简要描述和适用场景）

## 基础用法

(描述该板块的用法说明，并给出代码示例)

\`\`\`vue
<template>
  <${componentInfo.name} 属性="值"/>
</template>

<script setup>
import { ref } from 'vue'
import ${componentInfo.name} from '@/helper-components/${componentInfo.dirName}'
const value = ref(false)
</script>
\`\`\`

## API

### Props

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| modelValue | 绑定值 | \`boolean\` | \`false\` | 否 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 值改变时触发 | \`(value: boolean) => void\` |

## 注意事项
1. 使用注意事项
2. 常见问题说明


模版解释：
1.模版包含组件名，组件总体介绍，各板块的介绍及使用示例代码，props表格说明，event表格说明，slot插槽表格说明，注意事项说明
2.关于板块内容：第一个板块固定为基础用法，后续板块根据props和events的属性分析，生成对应的标题，如：禁用状态
3.每个板块的示例代码，要按照模版生成，一定是要可运行可渲染的完整vue代码
4.板块中的示例代码需要包含，<template>写组件的使用，使用vue3语法糖<script setup>方式写组件的导入及变量定义，要求代码示例要与该板块相关，如禁用状态则只展示disable属性的使用方法。

文档生成要求：
1. 仔细分析源码中的实际props和events
2. 表格格式必须正确，使用标准markdown语法
3. 代码示例要展示真实的使用方式
4. 类型和默认值要用反引号包围
5. 只输出markdown内容，不要解释

我提供了${referenceDoc}的radio组件的标准参考文档，请仔细阅读，并参考其格式生成文档。

直接输出markdown：`
  }

  /**
   * 清理和优化生成的markdown
   */
  cleanupMarkdown(markdownContent) {
    // 移除可能的代码块标记
    let cleaned = markdownContent.replace(/^```markdown\n/, '').replace(/\n```$/, '')

    // 移除开头的解释文字
    cleaned = cleaned.replace(/^以下是.*?文档：?\n\n?/, '')

    // 确保标题格式正确
    cleaned = cleaned.replace(/^#+\s*(.+)$/gm, (match, title) => {
      const level = match.match(/^#+/)[0].length
      return '#'.repeat(level) + ' ' + title.trim()
    })

    // 修复表格格式：将连续的表格行分开
    cleaned = cleaned.replace(/(\|[^|\n]+\|)(\s*)(\|[^|\n]+\|)/g, (match, row1, space, row2) => {
      // 如果两个表格行之间没有换行，添加换行
      if (!space.includes('\n')) {
        return row1 + '\n' + row2
      }
      return match
    })

    // 修复表格分隔符行
    cleaned = cleaned.replace(/\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|/g,
      '|------|------|------|--------|------|')

    // 确保表格行格式正确
    cleaned = cleaned.replace(/\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|/g,
      '| $1 | $2 | $3 | $4 | $5 |')

    // 移除重复的文档结构部分
    cleaned = cleaned.replace(/## 文档结构[\s\S]*$/, '')

    return cleaned.trim()
  }

  /**
   * 转换为PascalCase
   */
  toPascalCase(str) {
    return str
      .split(/[-_\s]+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('')
  }

  /**
   * 保存生成的文档
   */
  async saveDocument(componentInfo, markdownContent) {
    const docsDir = './docs/components'
    const docPath = `${docsDir}/${componentInfo.dirName}.md`
    
    // 确保目录存在
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true })
    }
    
    // 保存文档
    fs.writeFileSync(docPath, markdownContent, 'utf8')
    
    console.log(`💾 文档已保存: ${docPath}`)
    return docPath
  }
}

export default MarkdownDocGenerator
