/**
 * AI服务适配器
 * 支持多个AI模型，按优先级降级使用
 */

class AIService {
  constructor(config = {}) {
    // 直接使用传入的配置，不再重新构建
    this.config = config
    this.currentProvider = null
  }

  /**
   * 获取所有启用的AI服务，按优先级排序
   */
  getEnabledProviders() {
    // 定义优先级顺序：Gemini优先
    const priorityOrder = ['gemini', 'claude', 'ollama']

    return priorityOrder.filter(provider =>
      this.config[provider] && this.config[provider].enabled
    )
  }

  /**
   * 生成demo代码
   */
  async generateDemo(componentInfo, template, demoType = 'basic') {
    const prompt = this.buildPrompt(componentInfo, template, demoType)
    return await this.callAI(prompt)
  }

  /**
   * 统一的AI调用接口 - 根据配置自动选择模型
   */
  async callAI(prompt) {
    // 获取所有启用的AI服务，按优先级排序
    const enabledProviders = this.getEnabledProviders()

    if (enabledProviders.length === 0) {
      throw new Error('没有启用的AI服务，请在配置中启用至少一个服务')
    }

    console.log(`🔍 发现 ${enabledProviders.length} 个启用的AI服务: ${enabledProviders.join(', ')}`)

    for (const provider of enabledProviders) {
      try {
        console.log(`🤖 尝试使用 ${provider} (${this.config[provider].model}) 生成内容...`)
        const result = await this.callProvider(provider, prompt)

        this.currentProvider = provider
        console.log(`✅ ${provider} 生成成功`)
        this.currentProvider = provider
        return this.extractCode(result)

      } catch (error) {
        console.log(`❌ ${provider} 失败: ${error.message}`)
        continue
      }
    }

    throw new Error('所有启用的AI服务都不可用')
  }

  /**
   * 修复代码错误
   */
  async fixCode(originalCode, errorMessage, componentInfo) {
    const prompt = this.buildFixPrompt(originalCode, errorMessage, componentInfo)
    
    // 使用当前成功的provider，如果失败则降级
    const providers = this.currentProvider 
      ? [this.currentProvider, ...['ollama', 'claude'].filter(p => p !== this.currentProvider)]
      : ['ollama', 'claude']
    
    for (const provider of providers) {
      if (!this.config[provider].enabled) continue
      
      try {
        console.log(`🔧 使用 ${provider} 修复代码...`)
        const result = await this.callProvider(provider, prompt)
        return this.extractCode(result)
        
      } catch (error) {
        console.log(`❌ ${provider} 修复失败: ${error.message}`)
        continue
      }
    }
    
    throw new Error('代码修复失败，所有AI服务都不可用')
  }

  /**
   * 通用AI调用方法 - 根据配置自动构建API请求
   */
  async callProvider(provider, prompt) {
    const providerConfig = this.config[provider]
    if (!providerConfig) {
      throw new Error(`未知的AI服务: ${provider}`)
    }

    try {
      // 导入node-fetch
      const nodeFetch = await import('node-fetch')
      const fetchFn = nodeFetch.default

      // 构建API请求
      const apiRequest = this.buildAPIRequest(provider, prompt)

      const response = await fetchFn(apiRequest.url, {
        method: apiRequest.method,
        headers: apiRequest.headers,
        body: JSON.stringify(apiRequest.body)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`${provider} API error: ${response.status} ${response.statusText} - ${errorText}`)
      }

      const data = await response.json()
      return this.extractResponse(provider, data)

    } catch (error) {
      throw new Error(`${provider} connection failed: ${error.message}`)
    }
  }

  /**
   * 基于配置构建API请求
   */
  buildAPIRequest(provider, prompt) {
    const providerConfig = this.config[provider]
    const apiConfig = providerConfig.api

    // 构建URL
    const url = `${providerConfig.baseUrl}${apiConfig.endpoint}`

    // 处理headers，替换占位符
    const headers = {}
    for (const [key, value] of Object.entries(apiConfig.headers)) {
      headers[key] = this.replacePlaceholders(value, {
        apiKey: providerConfig.apiKey,
        model: providerConfig.model
      })
    }

    // 处理请求体，替换占位符
    const body = this.processRequestBody(apiConfig.requestBody, {
      model: providerConfig.model,
      prompt: prompt,
      messages: [{ role: 'user', content: prompt }]
    })

    return {
      url,
      method: apiConfig.method,
      headers,
      body
    }
  }

  /**
   * 替换字符串中的占位符
   */
  replacePlaceholders(template, values) {
    if (typeof template !== 'string') return template

    let result = template
    for (const [key, value] of Object.entries(values)) {
      result = result.replace(`{${key}}`, value)
    }
    return result
  }

  /**
   * 处理请求体，替换占位符
   */
  processRequestBody(template, values) {
    if (typeof template === 'string') {
      return this.replacePlaceholders(template, values)
    }

    if (Array.isArray(template)) {
      return template.map(item => this.processRequestBody(item, values))
    }

    if (typeof template === 'object' && template !== null) {
      const result = {}
      for (const [key, value] of Object.entries(template)) {
        if (typeof value === 'string' && value.startsWith('{') && value.endsWith('}')) {
          const placeholder = value.slice(1, -1)
          result[key] = values[placeholder] || value
        } else {
          result[key] = this.processRequestBody(value, values)
        }
      }
      return result
    }

    return template
  }

  /**
   * 从响应中提取内容
   */
  extractResponse(provider, data) {
    const providerConfig = this.config[provider]
    const extractor = providerConfig.api.responseExtractor

    try {
      // 使用配置的提取路径
      const content = this.getNestedValue(data, extractor)

      if (content && typeof content === 'string' && content.trim()) {
        return content
      } else {
        console.log(`⚠️  ${provider}响应内容为空或格式异常`)
        console.log('📄 原始响应:', JSON.stringify(data, null, 2))
        throw new Error(`${provider} API返回空内容或格式错误`)
      }
    } catch (error) {
      console.log(`❌ ${provider}响应解析失败:`, error.message)
      throw new Error(`${provider} 响应解析失败: ${error.message}`)
    }
  }

  /**
   * 根据路径获取嵌套对象的值
   */
  getNestedValue(obj, path) {
    return path.split(/[\.\[\]]/).filter(Boolean).reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj)
  }

  /**
   * 调用Ollama本地服务
   */
  async callOllama(prompt) {
    try {
      // 导入node-fetch
      const nodeFetch = await import('node-fetch')
      const fetchFn = nodeFetch.default

      const response = await fetchFn(`${this.config.ollama.baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.config.ollama.model,
          prompt: prompt,
          stream: false,
          options: {
            temperature: 0.1,
            top_p: 0.9
          }
        })
      })

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      return data.response
    } catch (error) {
      throw new Error(`Ollama connection failed: ${error.message}`)
    }
  }

  /**
   * 调用Claude API
   */
  async callClaude(prompt) {
    if (!this.config.claude.apiKey) {
      throw new Error('Claude API key not configured')
    }

    try {
      // 导入node-fetch
      const nodeFetch = await import('node-fetch')
      const fetchFn = nodeFetch.default

      const apiUrl = this.config.claude.baseUrl
      const response = await fetchFn(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.config.claude.apiKey,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: this.config.claude.model,
          max_tokens: 200000,
          temperature: 0.1,
          messages: [{ role: 'user', content: prompt }]
        })
      })

      if (!response.ok) {
        throw new Error(`Claude API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      return data.content[0].text
    } catch (error) {
      throw new Error(`Claude API failed: ${error.message}`)
    }
  }

  /**
   * 构建生成代码的提示词
   */
  buildPrompt(componentInfo, template, demoType) {
    return `请严格按照以下示例格式生成Vue代码：

正确示例：
\`\`\`vue
<template>
  <div>
    <HelperRadio v-model="value" label="示例标签" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import HelperRadio from 'helper-components/helper-radio/index.vue'

const value = ref(false)
</script>
\`\`\`

现在请为以下组件生成代码：
- 组件名：${componentInfo.name}
- 导入路径：${componentInfo.importPath}
- Props：${componentInfo.props?.map(p => `${p.name}(${p.type})`).join(', ') || '无'}
- Events：${componentInfo.events?.map(e => e.name).join(', ') || '无'}

重要要求：
1. 保持模板的基础结构不变
2. 在 template 的 div 内添加组件使用代码
3. 在 script setup 中添加必要的导入和逻辑
4. 必须使用vue3的 <script setup> 语法糖的语法
7. 使用 helper-components 别名导入
8. 导入路径必须是：${componentInfo.importPath}
9. 根据demo类型生成对应的功能展示
10. 代码简洁，无描述性文字
11. 确保语法正确，可直接运行

直接输出Vue代码，不要任何解释：`
  }

  /**
   * 构建修复代码的提示词
   */
  buildFixPrompt(originalCode, errorMessage, componentInfo) {
    return `请修复以下 Vue 代码中的错误：

原始代码：
\`\`\`vue
${originalCode}
\`\`\`

错误信息：
${errorMessage}

组件信息：
- 组件名：${componentInfo.name}
- 组件路径：${componentInfo.importPath}

请修复错误并输出完整的正确代码，确保：
1. 修复所有报告的错误
2. 保持代码的基本结构
3. 语法正确，可以正常渲染
4. 使用正确的导入路径

直接输出修复后的完整 Vue 代码，不要包含任何解释。`
  }

  /**
   * 从AI响应中提取代码
   */
  extractCode(response) {
    // 尝试提取代码块中的内容
    const vueCodeMatch = response.match(/```vue\n([\s\S]*?)\n```/)
    if (vueCodeMatch) {
      return vueCodeMatch[1]
    }

    const codeMatch = response.match(/```\n([\s\S]*?)\n```/)
    if (codeMatch) {
      return codeMatch[1]
    }

    // 如果没有代码块标记，直接返回响应内容
    return response.trim()
  }

  /**
   * 测试AI服务连接
   */
  async testConnection() {
    const testPrompt = "请回复：连接测试成功"
    
    console.log('🔍 测试AI服务连接...')
    
    for (const provider of ['ollama', 'claude']) {
      if (!this.config[provider].enabled) {
        console.log(`⏭️  跳过 ${provider}（已禁用）`)
        continue
      }
      
      try {
        const result = await this.callProvider(provider, testPrompt)
        console.log(`✅ ${provider} 连接成功: ${result.substring(0, 50)}...`)
      } catch (error) {
        console.log(`❌ ${provider} 连接失败: ${error.message}`)
      }
    }
  }
}

export default AIService
