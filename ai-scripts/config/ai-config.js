/**
 * AI服务配置
 */

export default {
  // Gemini API配置（优先）
  gemini: {
    enabled: true,
    baseUrl: 'https://max.brother99.top/v1',
    apiKey: 'sk-WTkZSaqRUfLLcKPFj1PY6kLSR9Df0fbsqLUATh7gZa1h6VwZ',
    model: 'gemini-2.5-pro',
    // API调用配置
    api: {
      endpoint: '/chat/completions',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer {apiKey}'  // {apiKey} 会被替换为实际的key
      },
      requestBody: {
        model: '{model}',  // {model} 会被替换为实际的模型名
        messages: '{messages}',  // {messages} 会被替换为实际的消息
        max_tokens: 20000,
        temperature: 0.1,
        stream: false
      },
      responseExtractor: 'choices[0].message.content'  // 响应内容提取路径
    }
  },

  // Claude API配置
  claude: {
    enabled: true,
    baseUrl: 'http://47.90.253.235/api-test',
    apiKey: '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',
    model: 'claude-4-sonnet',
    // API调用配置
    api: {
      endpoint: '/v1/messages',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': '{apiKey}',
        'anthropic-version': '2023-06-01'
      },
      requestBody: {
        model: '{model}',
        max_tokens: 20000,
        temperature: 0.1,
        messages: '{messages}'
      },
      responseExtractor: 'content[0].text'
    }
  },

  // Ollama配置
  ollama: {
    enabled: false,
    baseUrl: process.env.OLLAMA_URL || 'http://127.0.0.1:11434',
    model: process.env.OLLAMA_MODEL || 'llama3.2:1b',
    // API调用配置
    api: {
      endpoint: '/api/generate',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      requestBody: {
        model: '{model}',
        prompt: '{prompt}',  // Ollama使用prompt而不是messages
        stream: false,
        options: {
          temperature: 0.1,
          top_p: 0.9
        }
      },
      responseExtractor: 'response'
    }
  },

  // 生成配置
  generation: {
    maxRetries: 3,
    timeout: 30000, // 30秒超时
    temperature: 0.1 // 较低的温度，确保输出稳定
  },

  // 验证配置
  validation: {
    enableSyntaxCheck: true,
    enableImportCheck: true,
    enableStructureCheck: true
  }
}
