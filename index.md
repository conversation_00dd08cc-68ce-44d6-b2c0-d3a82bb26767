---
layout: home

hero:
  name: "Helper Components"
  text: "WeGame 组件库"
  tagline: 高质量的 Vue 3 组件库，为 WeGame 项目提供强大支持
  image:
    src: /logo.svg
    alt: Helper Components
  actions:
    - theme: brand
      text: 快速开始
      link: /guide/getting-started
    - theme: alt
      text: 组件总览
      link: /components/

features:
  - icon: 🚀
    title: 开箱即用
    details: 丰富的组件库，满足大部分业务场景，支持按需引入
  - icon: 🎨
    title: 精美设计
    details: 遵循 WeGame 设计规范，提供精美的界面和良好的用户体验
  - icon: 🔧
    title: 灵活配置
    details: 提供丰富的 API 和插槽，支持高度自定义和扩展
  - icon: 📱
    title: 响应式设计
    details: 支持各种屏幕尺寸，完美适配桌面端和移动端
  - icon: 🛠️
    title: TypeScript 支持
    details: 使用 TypeScript 开发，提供完整的类型定义
  - icon: 📚
    title: 完整文档
    details: 详细的文档和示例，帮助开发者快速上手
---

<style>
:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(120deg, #3c78d8 30%, #5d87dd);
}
</style> 