# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# VitePress build output
.vitepress/dist/
.vitepress/cache/

# Environment variables
.env
.env.local
.env.*.local

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Temporary folders
tmp/
temp/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Build directories
dist/
build/

# Cache directories
.cache/

# Lock files (comment out if you want to commit them)
# package-lock.json
# yarn.lock 