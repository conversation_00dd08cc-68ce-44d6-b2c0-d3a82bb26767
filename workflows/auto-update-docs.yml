name: Auto Update Documentation

on:
  push:
    branches: [master, main]
  workflow_dispatch:
    inputs:
      component_name:
        description: '指定组件名称 (可选)'
        required: false
        type: string

env:
  NODE_VERSION: '18'

jobs:
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      new-components: ${{ steps.detect.outputs.new-components }}
      updated-components: ${{ steps.detect.outputs.updated-components }}
      has-changes: ${{ steps.detect.outputs.has-changes }}
    steps:
      - name: Checkout master branch
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Detect component changes
        id: detect
        run: |
          # 检查是否是手动触发
          if [ "${{ github.event_name }}" = "workflow_dispatch" ] && [ "${{ github.event.inputs.component_name }}" != "" ]; then
            echo "手动指定组件: ${{ github.event.inputs.component_name }}"
            echo "new-components=${{ github.event.inputs.component_name }}" >> $GITHUB_OUTPUT
            echo "has-changes=true" >> $GITHUB_OUTPUT
            exit 0
          fi

          # 自动检测变更
          if [ "${{ github.event.before }}" = "0000000000000000000000000000000000000000" ]; then
            echo "初始提交，跳过变更检测"
            echo "has-changes=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          LAST_COMMIT="${{ github.event.before }}"
          CURRENT_COMMIT="${{ github.sha }}"

          echo "检测 $LAST_COMMIT..$CURRENT_COMMIT 之间的变更"

          # 检测新增的组件目录
          NEW_COMPONENTS=$(git diff --name-only $LAST_COMMIT $CURRENT_COMMIT | \
            grep -E '^[^/]+/.*' | \
            grep -v -E '^(\.github|scripts|assets|utils|composable|ui|README\.md)' | \
            cut -d'/' -f1 | \
            sort | uniq | \
            while read component; do
              if [ -d "$component" ] && ([ -f "$component/index.vue" ] || [ -f "$component/index.ts" ]); then
                echo "$component"
              fi
            done | tr '\n' ',' | sed 's/,$//')

          # 检测更新的组件
          UPDATED_COMPONENTS=$(git diff --name-only $LAST_COMMIT $CURRENT_COMMIT | \
            grep -E '^[^/]+/.*\.(vue|ts|js)$' | \
            grep -v -E '^(\.github|scripts|assets|utils|composable|ui)' | \
            cut -d'/' -f1 | \
            sort | uniq | \
            while read component; do
              if [ -d "$component" ] && ([ -f "$component/index.vue" ] || [ -f "$component/index.ts" ]); then
                echo "$component"
              fi
            done | tr '\n' ',' | sed 's/,$//')

          echo "新增组件: $NEW_COMPONENTS"
          echo "更新组件: $UPDATED_COMPONENTS"

          if [ -n "$NEW_COMPONENTS" ] || [ -n "$UPDATED_COMPONENTS" ]; then
            echo "has-changes=true" >> $GITHUB_OUTPUT
          else
            echo "has-changes=false" >> $GITHUB_OUTPUT
          fi

          echo "new-components=$NEW_COMPONENTS" >> $GITHUB_OUTPUT
          echo "updated-components=$UPDATED_COMPONENTS" >> $GITHUB_OUTPUT

  update-docs:
    needs: detect-changes
    if: needs.detect-changes.outputs.has-changes == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'helper-docs/package-lock.json'

      - name: Install dependencies
        working-directory: helper-docs
        run: npm ci

      - name: Generate docs for new components
        if: needs.detect-changes.outputs.new-components != ''
        working-directory: helper-docs
        run: |
          IFS=',' read -ra COMPONENTS <<< "${{ needs.detect-changes.outputs.new-components }}"
          for component in "${COMPONENTS[@]}"; do
            if [ ! -z "$component" ]; then
              echo "生成新组件文档: $component"
              npm run generate-docs "$component" || echo "Warning: Failed to generate docs for $component"
            fi
          done

      - name: Update existing component docs
        if: needs.detect-changes.outputs.updated-components != ''
        working-directory: helper-docs
        run: |
          IFS=',' read -ra COMPONENTS <<< "${{ needs.detect-changes.outputs.updated-components }}"
          for component in "${COMPONENTS[@]}"; do
            if [ ! -z "$component" ]; then
              echo "更新组件文档: $component"
              npm run update-docs "$component" || echo "Warning: Failed to update docs for $component"
            fi
          done

      - name: Build documentation
        working-directory: helper-docs
        run: npm run build

      - name: Commit and push changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add helper-docs/
          
          if git diff --staged --quiet; then
            echo "没有需要提交的变更"
          else
            COMPONENTS="${{ needs.detect-changes.outputs.new-components }},${{ needs.detect-changes.outputs.updated-components }}"
            COMPONENTS=$(echo "$COMPONENTS" | sed 's/^,//;s/,$//' | tr ',' ' ')
            
            if [ -n "$COMPONENTS" ]; then
              git commit -m "docs: 自动更新组件文档 ($COMPONENTS) 

Generated at: $(date)
Commit: ${{ github.sha }}"
            else
              git commit -m "docs: 自动更新文档

Generated at: $(date)
Commit: ${{ github.sha }}"
            fi
            
            git push
          fi

  deploy-docs:
    needs: [detect-changes, update-docs]
    if: always() && (needs.update-docs.result == 'success' || needs.detect-changes.outputs.has-changes == 'false')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'helper-docs/package-lock.json'

      - name: Install dependencies
        working-directory: helper-docs
        run: npm ci

      - name: Build documentation
        working-directory: helper-docs
        run: npm run build

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./helper-docs/.vitepress/dist
          publish_branch: gh-pages
          force_orphan: true
          user_name: 'github-actions[bot]'
          user_email: 'github-actions[bot]@users.noreply.github.com'
          commit_message: |
            Deploy docs for ${{ github.sha }}
            
            Generated at: ${{ steps.date.outputs.date }}

      - name: Comment on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const { owner, repo, number } = context.issue;
            const deployUrl = `https://${owner}.github.io/${repo}`;
            
            await github.rest.issues.createComment({
              owner,
              repo,
              issue_number: number,
              body: `📚 文档已自动更新并部署！
              
**预览地址**: ${deployUrl}

🔄 **本次更新**:
- 新增组件: \`${{ needs.detect-changes.outputs.new-components || '无' }}\`
- 更新组件: \`${{ needs.detect-changes.outputs.updated-components || '无' }}\`

构建时间: \`$(date)\``
            });

  notify-completion:
    needs: [detect-changes, update-docs, deploy-docs]
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Notify completion
        run: |
          echo "🎉 文档自动化流程完成!"
          echo "- 变更检测: ${{ needs.detect-changes.result }}"
          echo "- 文档更新: ${{ needs.update-docs.result }}"
          echo "- 文档部署: ${{ needs.deploy-docs.result }}"
          
          if [ "${{ needs.deploy-docs.result }}" = "success" ]; then
            echo "✅ 文档已成功部署到 GitHub Pages"
          else
            echo "❌ 部署过程中出现问题"
          fi 