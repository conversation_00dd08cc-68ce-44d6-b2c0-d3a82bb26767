{"name": "helper-components-docs", "version": "1.0.0", "description": "WeGame Helper Components Documentation", "type": "module", "scripts": {"dev": "vitepress dev", "demo": "vite --config example/vite.config.js", "build": "vitepress build", "preview": "vitepress preview", "serve": "vitepress serve", "generate-docs": "node scripts/generate-component-docs.js", "update-docs": "node scripts/update-component-docs.js", "analyze-components": "node scripts/analyze-all-components.js", "deploy": "npm run build && npm run deploy:gh-pages", "deploy:gh-pages": "gh-pages -d .vitepress/dist", "docs:generate-ai": "node scripts/ai-docs-generator.js", "docs:ai-single": "node scripts/ai-docs-generator.js --component", "docs:ai-force": "node scripts/ai-docs-generator.js --force", "docs:demo-only": "node scripts/ai-docs-generator.js --demo-only"}, "dependencies": {"@monaco-editor/loader": "^1.5.0", "@tencent/super-player": "^1.0.0", "@tencent/wegame-web-sdk": "^1.1.4", "@vue/repl": "^4.6.2", "@vueuse/core": "^10.1.0", "axios": "^1.11.0", "dayjs": "^1.11.7", "dotenv": "^17.2.1", "js-cookie": "^3.0.5", "loadjs": "^4.3.0", "monaco-editor": "^0.52.2", "node-fetch": "^3.3.2", "swiper": "^10.3.1", "vue": "^3.3.4", "ws": "^8.14.2"}, "devDependencies": {"@babel/parser": "^7.22.16", "@babel/traverse": "^7.22.17", "@babel/types": "^7.22.17", "@types/js-cookie": "^3.0.3", "@types/node": "^20.5.9", "@types/ws": "^8.5.10", "@vitejs/plugin-vue": "^4.3.4", "@vue/compiler-sfc": "^3.3.4", "fs-extra": "^11.1.1", "gh-pages": "^6.0.0", "glob": "^10.3.4", "gray-matter": "^4.0.3", "sass": "^1.69.5", "sass-embedded": "^1.89.2", "typescript": "^5.2.2", "vite": "^4.4.9", "vitepress": "^1.0.0-rc.15"}, "keywords": ["vue", "components", "documentation", "wegame"], "author": "WeGame Team", "license": "MIT"}