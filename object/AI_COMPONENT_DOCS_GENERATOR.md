# 🤖 组件文档自动生成指南

## 📋 任务说明

根据提供的组件源码，自动分析组件特性并生成完整的文档和 demo 文件。

## 🔧 执行步骤

### 步骤1：分析组件源码

**分析内容**：
1. **组件名称**：从文件路径或组件定义中提取
2. **Props 属性**：分析 `defineProps` 或 `props` 定义
3. **Events 事件**：分析 `defineEmits` 或 `$emit` 调用
4. **主要功能**：根据模板和逻辑推断组件用途
5. **特殊特性**：识别表单、展示、交互等特性

**分析输出格式**：
```
组件分析结果：
- 组件名称：ComponentName
- 组件路径：helper-components/component-name/index.vue
- 主要功能：[功能描述]
- Props 属性：
  - prop1: type - 说明
  - prop2: type - 说明
- Events 事件：
  - event1: 参数 - 说明
  - event2: 参数 - 说明
- 组件类型：表单组件/展示组件/交互组件/反馈组件
```

### 步骤2：规划 Demo 文件

**规划原则**：
- 必需：`basic.vue` - 基础用法
- 根据 Props 属性规划：
  - 有 `disabled` → `disabled.vue`
  - 有 `size` → `sizes.vue`
  - 有 `type` → `types.vue`
  - 有 `loading` → `loading.vue`
  - 有 `variant` → `variants.vue`
- 根据组件功能规划：
  - 表单组件 → `form-example.vue`
  - 有验证功能 → `validation.vue`
  - 有异步操作 → `async.vue`

**规划输出格式**：
```
Demo 文件规划：
1. basic.vue - 基础用法（必需）
2. disabled.vue - 禁用状态（因为有 disabled 属性）
3. sizes.vue - 不同尺寸（因为有 size 属性）
4. form-example.vue - 表单示例（因为是表单组件）
```

### 步骤3：生成 Demo 文件

**Demo 文件规范**：
1. **专精原则**：每个文件只展示一个特性
2. **样式最小化**：只使用基本布局样式，避免装饰性样式
3. **逻辑简化**：使用最简单的逻辑实现功能演示
4. **导入路径**：`helper-components/[component-path]/index.vue`

**Demo 文件模板**：

#### basic.vue 模板
```vue
<template>
  <div>
    <ComponentName 
      v-model="value" 
      :prop1="prop1Value"
      @event="handleEvent"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ComponentName from 'helper-components/[component-path]/index.vue'

const value = ref('')
const prop1Value = ref('')
const currentStatus = ref('')

const handleEvent = (data) => {
  currentStatus.value = data
}
</script>
```

#### 特性展示模板
```vue
<template>
  <div>
    <!-- 根据具体特性展示不同状态 -->
    <ComponentName :特性属性="value1" label="状态1" />
    <ComponentName :特性属性="value2" label="状态2" />
    <ComponentName :特性属性="value3" label="状态3" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ComponentName from 'helper-components/[component-path]/index.vue'

const value1 = ref('state1')
const value2 = ref('state2')
const value3 = ref('state3')
</script>
```

### 步骤4：生成 Markdown 文档

**路径对应关系**
- MD 文档中的 `<demo src="..."/>` 路径必须与步骤3生成的 Vue 文件路径完全一致
- 路径格式：`[component-name]/[demo-file-name].vue` (简化路径，插件会自动补全为 `../../demo/`)
- 确保每个 `<demo>` 标签都有对应的 Vue 文件

**文档结构**：
```markdown
# ComponentName 组件名称

组件的简短描述，说明主要功能和适用场景。

## 基础用法

最简单的用法说明。

<demo src="[component-name]/basic.vue"/>

## [根据步骤2规划的 demo 文件动态生成章节]

## [如规划了 disabled.vue] 禁用状态
设置 `disabled` 属性禁用组件。
<demo src="[component-name]/disabled.vue"/>

## [如规划了 sizes.vue] 不同尺寸
通过 `size` 属性设置组件尺寸。
<demo src="[component-name]/sizes.vue"/>

## [如规划了 types.vue] 不同类型
通过 `type` 属性设置组件类型。
<demo src="[component-name]/types.vue"/>

## [如规划了 form-example.vue] 表单示例
在表单中的使用示例。
<demo src="[component-name]/form-example.vue"/>

## API

### Props
| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| [根据步骤1分析结果填写] | 属性说明 | `type` | `default` | 是/否 |

### Events
| 事件名 | 说明 | 参数 |
|--------|------|------|
| [根据步骤1分析结果填写] | 事件说明 | `(param: type) => void` |

## 注意事项

- [根据组件特性列出使用注意点]
- [常见问题和解决方案]
- [最佳实践建议]
```

**路径检查清单**：
- [ ] 每个 `<demo src="..."/>` 都有对应的 Vue 文件
- [ ] 路径格式正确：`../../demo/[component-name]/[file-name].vue`
- [ ] 文件名与步骤2规划的完全一致

## 📋 输出要求

**最终输出**：
1. **分析报告**：组件分析结果和 demo 文件规划
2. **Demo 文件**：所有规划的 .vue 文件的完整代码
3. **文档文件**：完整的 .md 文档代码

**文件命名规范**：
- Demo 文件：`demo/[component-name]/[feature].vue`
- 文档文件：`docs/components/[component-name].md`

**关键要求 - 路径一致性**：
- MD 文档中每个 `<demo src="..."/>` 必须对应一个实际的 Vue 文件
- 路径格式必须是：`[component-name]/[file-name].vue` (简化格式，插件自动补全)
- 文件名必须与步骤2规划的文件名完全一致
- 不能有多余的 `<demo>` 标签，也不能遗漏任何规划的文件

**质量标准**：
- Demo 代码简洁，无冗余样式
- 每个 demo 只展示一个功能特性
- 文档结构清晰，API 完整准确
- 路径对应关系正确，确保 demo 能正常显示
- 与 helper-radio 组件的文档风格保持一致

## 🎯 参考标准

严格参考以下现有文件的风格和结构：
- `demo/helper-radio/basic.vue`
- `demo/helper-radio/disabled.vue`
- `demo/helper-radio/custom-id.vue`
- `docs/components/helper-radio.md`

## 📝 使用示例

**输入**：提供组件源码
**输出**：按照上述4个步骤，依次输出分析结果、规划方案、demo文件代码、文档代码

**示例流程**：
```
用户：[提供 HelperButton 组件源码]

AI 输出：
### 步骤1：分析组件源码
组件分析结果：
- 组件名称：HelperButton
- 组件路径：helper-components/helper-button/index.vue
- 主要功能：按钮组件，支持点击交互
- Props 属性：
  - type: string - 按钮类型 (primary/secondary/danger)
  - size: string - 按钮尺寸 (small/medium/large)
  - disabled: boolean - 是否禁用
  - loading: boolean - 是否加载中
- Events 事件：
  - click: MouseEvent - 点击事件
- 组件类型：交互组件

### 步骤2：规划 Demo 文件
Demo 文件规划：
1. demo/helper-button/basic.vue - 基础用法（必需）
2. demo/helper-button/types.vue - 不同类型（因为有 type 属性）
3. demo/helper-button/sizes.vue - 不同尺寸（因为有 size 属性）
4. demo/helper-button/disabled.vue - 禁用状态（因为有 disabled 属性）
5. demo/helper-button/loading.vue - 加载状态（因为有 loading 属性）

### 步骤3：生成 Demo 文件
[输出所有 demo 文件的完整代码，文件路径与步骤2规划一致]

### 步骤4：生成 Markdown 文档
[输出完整的 .md 文档代码，其中 <demo> 路径使用简化格式：
- <demo src="helper-button/basic.vue"/>
- <demo src="helper-button/types.vue"/>
- <demo src="helper-button/sizes.vue"/>
- <demo src="helper-button/disabled.vue"/>
- <demo src="helper-button/loading.vue"/>]
```

确保生成的文档与现有文档风格完全一致。
