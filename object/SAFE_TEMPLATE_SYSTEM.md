# 🛡️ 安全模板系统设计

## 📋 模板系统概述

### 核心理念
- **结构固定**: 模板提供安全的基础结构
- **内容可变**: AI 在模板基础上生成完整代码
- **简洁实用**: 单一模板适用所有场景
- **错误可控**: 限制 AI 可能犯错的范围

## 🎯 通用安全模板

### 基础模板 (safe-demo-template.vue)
```vue
<template>
  <div>
    <!-- AI 在这里生成组件使用代码 -->
  </div>
</template>

<script setup>
import { ref } from 'vue'
// AI 在这里生成导入语句和逻辑代码
</script>
```

## 🚀 使用方式

### AI 提示词模板
```
请根据以下安全模板生成完整的 Vue demo 文件：

模板：
```vue
<template>
  <div>
    <!-- AI 在这里生成组件使用代码 -->
  </div>
</template>

<script setup>
import { ref } from 'vue'
// AI 在这里生成导入语句和逻辑代码
</script>
```

组件信息：
- 组件名：{{COMPONENT_NAME}}
- 组件路径：helper-components/{{COMPONENT_PATH}}/index.vue
- Props：{{PROPS_LIST}}
- Events：{{EVENTS_LIST}}
- 功能描述：{{DESCRIPTION}}

要求：
1. 保持模板的基础结构不变
2. 在 template 的 div 内添加组件使用代码
3. 在 script setup 中添加必要的导入和逻辑
4. 代码简洁，无描述性文字
5. 确保语法正确，可直接运行
6. 使用 helper-components 别名导入

直接输出完整的 Vue 文件代码，不要包含任何解释。
```

## 🎯 优势

### 1. 简洁实用
- 单一模板，适用所有场景
- AI 直接生成完整代码
- 无需复杂的插槽拼接

### 2. 安全可控
- 固定的基础结构
- 限制 AI 的修改范围
- 减少语法错误风险

### 3. 易于维护
- 模板简单明了
- 便于理解和修改
- 扩展性好

---

这个简化的模板系统更加实用，AI 可以直接基于模板生成完整的 Vue 文件。
