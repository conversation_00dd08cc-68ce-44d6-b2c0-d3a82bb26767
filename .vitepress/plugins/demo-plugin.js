import fs from 'fs'
import path from 'path'

export function demoPlugin() {
  return {
    name: 'demo-plugin',
    enforce: /** @type {'pre'} */ ('pre'), // 确保在其他插件之前执行
    transform(code, id) {
      // 只处理 .md 文件
      if (!id.endsWith('.md')) {
        return null
      }

      // 检查是否包含 demo 标签
      if (!code.includes('<demo')) {
        return null
      }

      // 收集所有需要导入的组件
      const imports = new Set()

      // 处理 <demo src="xxx"/> 标记
      let transformedCode = code.replace(
        /<demo\s+src="([^"]+)"\s*\/>/g,
        (match, src) => {
          try {
            // 解析路径：支持简化路径和完整路径
            const mdDir = path.dirname(id)
            let demoPath

            if (src.startsWith('../../demo/')) {
              // 完整路径：../../demo/helper-radio/basic.vue
              demoPath = path.resolve(mdDir, src)
            } else {
              // 简化路径：helper-radio/basic.vue -> ../../demo/helper-radio/basic.vue
              demoPath = path.resolve(mdDir, '../../demo', src)
            }

            // 检查文件是否存在
            if (!fs.existsSync(demoPath)) {
              console.error(`Demo file not found: ${demoPath}`)
              return `<div style="color: red;">Demo file not found: ${src}</div>`
            }

            // 读取 demo 文件内容
            const demoCode = fs.readFileSync(demoPath, 'utf-8')

            // 生成组件名（基于文件名，处理连字符）
            const fileName = path.basename(src, '.vue')
            const componentName = fileName
              .split('-')
              .map(part => part.charAt(0).toUpperCase() + part.slice(1))
              .join('') + 'Demo'

            // 生成相对导入路径
            const relativePath = path.relative(mdDir, demoPath).replace(/\\/g, '/')

            // 添加到导入集合
            imports.add(`import ${componentName} from '${relativePath}'`)

            return `<DemoContainer>
<template #demo>
<${componentName} />
</template>
<template #code>

\`\`\`vue
${demoCode}
\`\`\`

</template>
</DemoContainer>`
          } catch (error) {
            console.error(`Error processing demo: ${src}`, error)
            return `<div style="color: red;">Error loading demo: ${src}</div>`
          }
        }
      )

      // 如果有变化，添加 script setup
      if (transformedCode !== code && imports.size > 0) {
        const scriptSetup = `<script setup>
${Array.from(imports).join('\n')}
</script>

`

        // 在文档开头添加 script setup
        transformedCode = scriptSetup + transformedCode
        return {
          code: transformedCode,
          map: null
        }
      }

      return null
    }
  }
}
