import DefaultTheme from 'vitepress/theme'
import { h } from 'vue'
import type { Theme } from 'vitepress'
import './custom.css'

// 导入组件demo容器
import DemoContainer from '../components/DemoContainer.vue'

export default {
  extends: DefaultTheme,
  Layout: () => {
    return h(DefaultTheme.Layout, null, {
      // https://vitepress.dev/guide/extending-default-theme#layout-slots
    })
  },
  enhanceApp({ app, router, siteData }) {
    // 注册全局组件
    app.component('DemoContainer', DemoContainer)
    
    // 动态注册组件demos
    const demoModules = (import.meta as any).glob('../demos/**/*.vue', { eager: true })
    Object.entries(demoModules).forEach(([path, module]: [string, any]) => {
      // 从路径中提取完整的组件名，例如：
      // '../demos/button-copy/basic.vue' -> 'ButtonCopyBasic'
      const relativePath = path.replace('../demos/', '').replace('.vue', '')
      const componentName = pathToComponentName(relativePath)
      
      console.log(`注册组件: ${componentName} (来自 ${path})`)
      
      if (componentName && module.default) {
        app.component(componentName, module.default)
      }
    })
  }
} satisfies Theme

function pathToComponentName(demoPath: string): string {
  // button-copy/basic -> ButtonCopyBasic
  return demoPath
    .split('/')
    .map(part => 
      part.split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join('')
    )
    .join('')
} 