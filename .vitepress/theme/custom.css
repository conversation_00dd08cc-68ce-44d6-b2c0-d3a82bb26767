/**
 * Customize default theme styling by overriding CSS variables:
 * https://github.com/vuejs/vitepress/blob/main/src/client/theme-default/styles/vars.css
 */

/**
 * Colors
 *
 * Each colors have exact same color scale system with 3 levels of solid
 * colors with different brightness, and 1 soft color.
 * 
 * - `XXX-1`: The most solid color used mainly for colored text. It must
 *   satisfy the contrast ratio against when used on top of `XXX-soft`.
 *
 * - `XXX-2`: The color used mainly for hover state of the button.
 *
 * - `XXX-3`: The color for solid background, such as bg color of the button.
 *   It must satisfy the contrast ratio with pure white (#ffffff) text on
 *   top of it.
 *
 * - `XXX-soft`: The color used for subtle background such as custom container
 *   or badges. It must satisfy the contrast ratio when putting `XXX-1` colors
 *   on top of it.
 *
 *   The soft color must be semi transparent alpha channel. This is crucial
 *   because it allows adding multiple "soft" colors on top of each other
 *   to create a accent, such as when having inline code block inside
 *   custom containers.
 *
 * - `default`: The color used purely for subtle indication without any
 *   special meanings attched to it such as bg color for menu hover state.
 *
 * - `brand`: Used for primary brand colors, such as link text, button with
 *   brand theme, etc.
 *
 * - `tip`: Used to indicate useful information. The default theme uses the
 *   brand color for this by default.
 *
 * - `warning`: Used to indicate warning to the users. Used in custom
 *   container, badges, etc.
 *
 * - `danger`: Used to indicate dangerous message to the users. Used in custom
 *   container, badges, etc.
 * -------------------------------------------------------------------------- */

:root {
  --vp-c-default-1: var(--vp-c-gray-1);
  --vp-c-default-2: var(--vp-c-gray-2);
  --vp-c-default-3: var(--vp-c-gray-3);
  --vp-c-default-soft: var(--vp-c-gray-soft);

  --vp-c-brand-1: #3c78d8;
  --vp-c-brand-2: #5d87dd;
  --vp-c-brand-3: #3c78d8;
  --vp-c-brand-soft: rgba(60, 120, 216, 0.14);

  --vp-c-tip-1: var(--vp-c-brand-1);
  --vp-c-tip-2: var(--vp-c-brand-2);
  --vp-c-tip-3: var(--vp-c-brand-3);
  --vp-c-tip-soft: var(--vp-c-brand-soft);

  --vp-c-warning-1: var(--vp-c-yellow-1);
  --vp-c-warning-2: var(--vp-c-yellow-2);
  --vp-c-warning-3: var(--vp-c-yellow-3);
  --vp-c-warning-soft: var(--vp-c-yellow-soft);

  --vp-c-danger-1: var(--vp-c-red-1);
  --vp-c-danger-2: var(--vp-c-red-2);
  --vp-c-danger-3: var(--vp-c-red-3);
  --vp-c-danger-soft: var(--vp-c-red-soft);
}

/**
 * Component: Button
 * -------------------------------------------------------------------------- */

:root {
  --vp-button-brand-border: transparent;
  --vp-button-brand-text: var(--vp-c-white);
  --vp-button-brand-bg: var(--vp-c-brand-3);
  --vp-button-brand-hover-border: transparent;
  --vp-button-brand-hover-text: var(--vp-c-white);
  --vp-button-brand-hover-bg: var(--vp-c-brand-2);
  --vp-button-brand-active-border: transparent;
  --vp-button-brand-active-text: var(--vp-c-white);
  --vp-button-brand-active-bg: var(--vp-c-brand-1);
}

/**
 * Component: Home
 * -------------------------------------------------------------------------- */

:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(
    120deg,
    #3c78d8 30%,
    #5d87dd
  );

  --vp-home-hero-image-background-image: linear-gradient(
    -45deg,
    #3c78d8 50%,
    #5d87dd 50%
  );
  --vp-home-hero-image-filter: blur(44px);
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }
  .vp-doc div[class*='language-'], .vp-block{
    margin: 0;
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(68px);
  }
}

/**
 * Component: Custom Block
 * -------------------------------------------------------------------------- */

:root {
  --vp-custom-block-tip-border: transparent;
  --vp-custom-block-tip-text: var(--vp-c-text-1);
  --vp-custom-block-tip-code-bg: var(--vp-c-brand-soft);
  --vp-c-divider: var(--vp-custom-block-tip-bg);
  --vp-sidebar-bg-color:#f6f6f7d1;
  --vp-c-border: var(--vp-custom-block-tip-bg);
}

/**
 * Component: Algolia
 * -------------------------------------------------------------------------- */

.DocSearch {
  --docsearch-primary-color: var(--vp-c-brand-1) !important;
}

/**
 * Custom Demo Container Styles
 * -------------------------------------------------------------------------- */

.demo-container {
  overflow: hidden;
  margin: 16px;
}

.demo-preview {
  padding: 24px;
  background-color: var(--vp-c-bg);
  border-bottom: 1px solid var(--vp-custom-block-tip-bg);
}

.demo-code {
  background-color: var(--vp-code-block-bg);
}

.demo-controls {
  padding: 16px;
  background-color: var(--vp-c-bg-soft);
  border-top: 1px solid var(--vp-c-border);
}

.demo-control {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.demo-control:last-child {
  margin-bottom: 0;
}

.demo-control label {
  min-width: 100px;
  font-weight: 500;
  color: var(--vp-c-text-1);
}

.demo-control input,
.demo-control select {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  background-color: var(--vp-c-bg);
  color: var(--vp-c-text-1);
  font-size: 14px;
}

.demo-control input:focus,
.demo-control select:focus {
  outline: none;
  border-color: var(--vp-c-brand-1);
}

/* 组件特定样式 */
.component-preview {
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .demo-control {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .demo-control label {
    min-width: auto;
  }

  .demo-control input,
  .demo-control select {
    width: 100%;
  }
}

/**
 * Global Scrollbar Styles
 * -------------------------------------------------------------------------- */

/* 全局滚动条样式 - 与 DemoContainer 保持一致 */
::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--vp-custom-block-tip-bg);
  border-radius: 1px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--vp-custom-block-tip-bg);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--vp-custom-block-tip-bg) transparent;
}

/**
 * 代码编辑器相关样式优化
 * -------------------------------------------------------------------------- */
.monaco-editor {
  --monaco-editor-background: var(--vp-code-block-bg);
  --monaco-editor-foreground: var(--vp-code-color);
}

/* 确保Monaco Editor在暗色模式下正确显示 */
.dark .monaco-editor {
  --monaco-editor-background: #1e1e1e;
  --monaco-editor-foreground: #d4d4d4;
}

/* 代码编辑器容器的滚动条样式 */
.code-editor-container ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.code-editor-container ::-webkit-scrollbar-track {
  background: var(--vp-c-bg-soft);
}

.code-editor-container ::-webkit-scrollbar-thumb {
  background: var(--vp-c-border);
  border-radius: 3px;
}

.code-editor-container ::-webkit-scrollbar-thumb:hover {
  background: var(--vp-c-text-3);
}

/* 优化demo容器的编辑按钮样式 */
.demo-edit-btn .demo-icon {
  transition: transform 0.2s ease;
}

.demo-edit-btn:hover .demo-icon {
  transform: scale(1.1);
}

/* 确保编辑器在移动端的可用性 */
@media (max-width: 640px) {
  .code-editor-container .editor-body {
    min-height: 300px;
  }

  .code-editor-container .monaco-editor-container {
    font-size: 12px;
  }
}

/* Monaco Editor 主题适配 */
.monaco-editor .margin,
.monaco-editor .monaco-editor-background {
  background-color: var(--vp-code-block-bg) !important;
}

.monaco-editor .current-line {
  background-color: var(--vp-c-bg-soft) !important;
}

/* 编辑器工具栏样式优化 */
.editor-header {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.preview-iframe {
  background: var(--vp-c-bg);
}