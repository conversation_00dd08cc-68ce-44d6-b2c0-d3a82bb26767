<template>
  <div class="simple-code-editor">
    <!-- 编辑器头部 -->
    <div class="editor-header">
      <div class="editor-tabs">
        <span class="tab active">App.vue</span>
      </div>
      <div class="editor-actions">
        <button class="action-btn" @click="formatCode" title="格式化代码">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </button>
        <button class="action-btn" @click="resetCode" title="重置代码">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
          </svg>
        </button>
        <button class="action-btn" @click="copyCode" title="复制代码">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
          </svg>
        </button>
        <button class="action-btn" @click="openInPlayground" title="在新窗口打开">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 编辑器主体 -->
    <div class="editor-body">
      <!-- 代码编辑区域 -->
      <div class="editor-panel">
        <textarea
          ref="codeTextarea"
          v-model="code"
          class="code-textarea"
          spellcheck="false"
          @input="onCodeChange"
        ></textarea>
      </div>

      <!-- 预览区域 -->
      <div class="preview-panel">
        <div class="preview-header">
          <span>预览</span>
          <button class="refresh-btn" @click="refreshPreview" title="刷新预览">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
            </svg>
          </button>
        </div>
        <div class="preview-content">
          <iframe
            ref="previewFrame"
            class="preview-iframe"
            sandbox="allow-scripts allow-same-origin allow-modals"
            @load="onPreviewLoad"
          ></iframe>
        </div>
      </div>
    </div>

    <!-- 错误信息显示 -->
    <div v-if="error" class="error-panel">
      <div class="error-header">
        <span>错误信息</span>
        <button @click="error = ''" class="close-error">×</button>
      </div>
      <pre class="error-content">{{ error }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'

const props = defineProps({
  initialCode: {
    type: String,
    default: `<template>
  <div class="demo">
    <h3>{{ title }}</h3>
    <p>{{ message }}</p>
    <button @click="handleClick">点击我</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const title = ref('Hello World')
const message = ref('这是一个可编辑的Vue组件示例')

const handleClick = () => {
  message.value = '按钮被点击了！'
}
<\/script>

<style scoped>
.demo {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  text-align: center;
}

button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}
</style>`
  },
  height: {
    type: String,
    default: '500px'
  }
})

const emit = defineEmits(['code-change'])

// 响应式数据
const codeTextarea = ref(null)
const previewFrame = ref(null)
const code = ref(props.initialCode)
const error = ref('')

// 防抖更新预览
let updatePreviewTimer = null
const debounceUpdatePreview = () => {
  if (updatePreviewTimer) {
    clearTimeout(updatePreviewTimer)
  }
  updatePreviewTimer = setTimeout(() => {
    updatePreview()
  }, 500)
}

// 代码变化处理
const onCodeChange = () => {
  emit('code-change', code.value)
  debounceUpdatePreview()
}

// 更新预览
const updatePreview = async () => {
  try {
    error.value = ''
    if (!previewFrame.value) return

    // 创建预览HTML
    const previewHtml = createPreviewHtml(code.value)
    
    // 更新iframe内容
    const iframe = previewFrame.value
    const doc = iframe.contentDocument || iframe.contentWindow.document
    doc.open()
    doc.write(previewHtml)
    doc.close()
  } catch (err) {
    error.value = err.message
    console.error('Preview update error:', err)
  }
}

// 创建预览HTML
const createPreviewHtml = (vueCode) => {
  // 简单的Vue SFC解析
  const templateMatch = vueCode.match(/<template>([\s\S]*?)<\/template>/)
  const scriptMatch = vueCode.match(/<script[^>]*>([\s\S]*?)<\/script>/)
  const styleMatch = vueCode.match(/<style[^>]*>([\s\S]*?)<\/style>/)

  const template = templateMatch ? templateMatch[1].trim() : '<div>No template found</div>'
  const script = scriptMatch ? scriptMatch[1].trim() : ''
  const style = styleMatch ? styleMatch[1].trim() : ''

  const html = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"><\/script>
  <style>
    body { 
      margin: 0; 
      padding: 16px; 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: var(--vp-c-bg, #ffffff);
      color: var(--vp-c-text-1, #213547);
    }
    ${style}
  </style>
</head>
<body>
  <div id="app"></div>
  <script>
    try {
      const { createApp, ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } = Vue;
      
      ${script.replace(/import\s+.*?from\s+['"][^'"]*['"];?\s*/g, '').replace(/export\s+default\s+/, '')}
      
      const app = createApp({
        template: \`${template.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`,
        setup() {
          ${script.includes('setup()') ? script : 'return {}'}
        }
      });
      
      app.mount('#app');
    } catch (error) {
      document.body.innerHTML = '<div style="color: red; padding: 20px;"><h3>Error:</h3><pre>' + error.message + '</pre></div>';
      console.error('Preview error:', error);
    }
  <\/script>
</body>
</html>`
  
  return html
}

// 工具函数
const formatCode = () => {
  // 简单的代码格式化
  try {
    // 这里可以添加更复杂的格式化逻辑
    console.log('格式化代码')
  } catch (err) {
    console.error('格式化失败:', err)
  }
}

const resetCode = () => {
  code.value = props.initialCode
  updatePreview()
}

const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(code.value)
    console.log('代码已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
  }
}

const openInPlayground = () => {
  const encoded = encodeURIComponent(code.value)
  const url = `data:text/html,${encoded}`
  window.open(url, '_blank')
}

const refreshPreview = () => {
  updatePreview()
}

const onPreviewLoad = () => {
  nextTick(() => {
    updatePreview()
  })
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    updatePreview()
  })
})

// 监听props变化
watch(() => props.initialCode, (newCode) => {
  if (newCode !== code.value) {
    code.value = newCode
    updatePreview()
  }
}, { immediate: true })
</script>
