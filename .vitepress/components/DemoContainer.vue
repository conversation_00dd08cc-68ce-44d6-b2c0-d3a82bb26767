<template>
  <div class="demo-containers">
    <div class="demo-preview">
      <div class="demo-content">
        <slot name="demo" />
      </div>
    </div>

    <div v-if="showCode" class="demo-code">
      <div class="demo-code-content">
        <slot name="code" />
      </div>
    </div>

    <div v-if="$slots.controls" class="demo-controls">
      <slot name="controls" />
    </div>

    <div class="demo-actions">
      <button
        class="demo-toggle-btn"
        @click="showCode = !showCode"
        :class="{ 'is-active': showCode }"
      >
        <svg class="demo-icon" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
        </svg>
        <span>{{ showCode ? '隐藏代码' : '查看代码' }}</span>
      </button>

      <div class="demo-edit-actions">
        <button
          class="demo-edit-btn"
          @click="showEditor = !showEditor"
          :class="{ 'is-active': showEditor }"
          title="编辑代码"
        >
          <svg class="demo-icon" viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
          </svg>
          <span>{{ showEditor ? '关闭编辑' : '编辑代码' }}</span>
        </button>

        <button
          class="demo-edit-btn"
          @click="openInNewWindow"
          title="在新窗口中编辑"
        >
          <svg class="demo-icon" viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
          </svg>
          <span>新窗口</span>
        </button>

        <button
          class="demo-edit-btn"
          @click="copyCode"
          title="复制代码"
        >
          <svg class="demo-icon" viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
          </svg>
          <span>复制</span>
        </button>
      </div>
    </div>

    <!-- 代码编辑器 -->
    <div v-if="showEditor" class="demo-editor">
      <CodeEditor
        :initial-code="demoCode"
        :height="'400px'"
        @code-change="onCodeChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import CodeEditor from './CodeEditor.vue'

const props = defineProps({
  demoCode: {
    type: String,
    default: ''
  }
})

const showCode = ref(false)
const showEditor = ref(false)

// 从slot中提取代码内容
const demoCode = computed(() => {
  if (props.demoCode) {
    return props.demoCode
  }

  // 尝试从code slot中提取代码
  try {
    // 这里我们需要一个更好的方法来获取slot内容
    // 暂时返回默认示例
    return `<template>
  <div class="demo">
    <p>这是一个示例组件</p>
    <button @click="handleClick">点击我</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const message = ref('Hello World!')

const handleClick = () => {
  message.value = '按钮被点击了！'
}
<\/script>

<style scoped>
.demo {
  padding: 20px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 8px;
}

button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

button:hover {
  background: #0056b3;
}
</style>`
  } catch (error) {
    console.error('Error extracting demo code:', error)
    return '// 无法提取代码内容'
  }
})

// 代码变化处理
const onCodeChange = (newCode) => {
  // 这里可以实现实时更新demo预览
  console.log('Code changed:', newCode)
}

// 复制代码
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(demoCode.value)
    // 可以添加成功提示
    console.log('代码已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 在新窗口中打开
const openInNewWindow = () => {
  const encoded = encodeURIComponent(demoCode.value)
  const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>代码编辑器</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"><\/script>
  <style>
    body { margin: 0; font-family: system-ui; }
    .container { display: flex; height: 100vh; }
    .editor { flex: 1; }
    .preview { flex: 1; border-left: 1px solid #ddd; }
    textarea { width: 100%; height: 100%; border: none; padding: 16px; font-family: monospace; }
  </style>
</head>
<body>
  <div class="container">
    <div class="editor">
      <textarea id="code">${demoCode.value}</textarea>
    </div>
    <div class="preview">
      <iframe id="preview" style="width: 100%; height: 100%; border: none;"></iframe>
    </div>
  </div>
  <script>
    // 简单的实时预览实现
    const code = document.getElementById('code');
    const preview = document.getElementById('preview');

    function updatePreview() {
      const vueCode = code.value;
      // 这里可以添加更复杂的Vue SFC编译逻辑
      const html = '<div>' + vueCode + '</div>';
      preview.srcdoc = html;
    }

    code.addEventListener('input', updatePreview);
    updatePreview();
  <\/script>
</body>
</html>`

  const blob = new Blob([html], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  window.open(url, '_blank')
}
</script>

<style scoped>
.demo-containers {
  border: 1px solid var(--vp-c-border);
  border-radius: 12px;
  overflow: hidden;
  margin: 24px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.2s ease;
}

.demo-containers:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.demo-preview {
  background-color: var(--vp-c-bg);
  position: relative;
}

.demo-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
  /* background: linear-gradient(135deg, var(--vp-c-bg) 0%, var(--vp-c-bg-soft) 100%); */
}

.demo-code {
  background-color: var(--vp-code-block-bg);
  border-top: 1px solid var(--vp-c-divider);
}

.demo-code-content {
  max-height: 500px;
  overflow-y: auto;
}

.demo-actions {
  padding: 0;
  background: linear-gradient(90deg, var(--vp-c-bg-soft) 0%, var(--vp-c-bg-mute) 100%);
  border-top: 1px solid var(--vp-c-divider);
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-edit-actions {
  display: flex;
  gap: 0;
  margin-right: 8px;
}

.demo-edit-btn {
  padding: 8px 12px;
  background: transparent;
  color: #869dc0;
  border: none;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  margin-left: 4px;
}

.demo-edit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.demo-edit-btn:hover::before {
  left: 100%;
}

.demo-edit-btn:hover {
  color: rgb(94, 130, 192);
  background: rgba(94, 130, 192, 0.1);
}

.demo-edit-btn.is-active {
  color: rgb(94, 130, 192);
  background: rgba(94, 130, 192, 0.15);
}

.demo-editor {
  border-top: 1px solid var(--vp-c-divider);
  background: var(--vp-c-bg);
}

.demo-toggle-btn {
  width: 100%;
  padding: 3px 20px;
  background: transparent;
  color: #869dc0;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.demo-preview{
  padding: 0 !important;
}

.demo-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.demo-toggle-btn:hover::before {
  left: 100%;
}

.demo-toggle-btn:hover {
  color:rgb(94, 130, 192);
}

.demo-toggle-btn.is-active {
  color: rgb(94, 130, 192);
}

.demo-toggle-btn.is-active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
}

.demo-icon {
  transition: transform 0.3s ease;
  opacity: 0.8;
}

.demo-toggle-btn:hover .demo-icon {
  transform: scale(1.1);
  opacity: 1;
}

.demo-toggle-btn.is-active .demo-icon {
  transform: rotate(180deg);
}

/* 滚动条样式 */
.demo-content::-webkit-scrollbar,
.demo-code-content::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

.demo-content::-webkit-scrollbar-track,
.demo-code-content::-webkit-scrollbar-track {
  background: transparent;
}

.demo-content::-webkit-scrollbar-thumb,
.demo-code-content::-webkit-scrollbar-thumb {
  background: var(--vp-custom-block-tip-bg);
  border-radius: 1px;
  transition: background 0.2s ease;
}

.demo-content::-webkit-scrollbar-thumb:hover,
.demo-code-content::-webkit-scrollbar-thumb:hover {
  background: var(--vp-custom-block-tip-bg);
}

/* 滚动条交叉角落 */
.demo-content::-webkit-scrollbar-corner,
.demo-code-content::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条样式 */
.demo-content,
.demo-code-content {
  scrollbar-width: thin;
  scrollbar-color: var(--vp-custom-block-tip-bg) transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-containers {
    margin: 16px 0;
    border-radius: 8px;
  }

  .demo-content {
    max-height: 300px;
  }

  .demo-code-content {
    max-height: 250px;
  }

  .demo-toggle-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .demo-actions {
    flex-direction: column;
    gap: 8px;
    padding: 8px;
  }

  .demo-edit-actions {
    justify-content: center;
    margin-right: 0;
  }

  .demo-edit-btn {
    padding: 6px 10px;
    font-size: 11px;
  }

  .demo-edit-btn span {
    display: none;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .demo-containers {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .demo-containers:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}
</style>