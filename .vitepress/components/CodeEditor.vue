<template>
  <div class="code-editor">
    <div class="editor-header">
      <div class="editor-title">
        <span>Vue 组件编辑器</span>
      </div>
      <div class="editor-actions">
        <button class="editor-btn" @click="runCode" title="运行代码">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M8 5v14l11-7z"/>
          </svg>
        </button>
        <button class="editor-btn" @click="resetCode" title="重置代码">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
          </svg>
        </button>
      </div>
    </div>

    <div class="editor-content">
      <div class="editor-pane">
        <textarea
          v-model="currentCode"
          class="code-textarea"
          placeholder="在这里编辑Vue组件代码..."
          @input="onCodeChange"
        ></textarea>
      </div>

      <div class="preview-pane">
        <div class="preview-header">
          <span>预览</span>
          <div class="preview-actions">
            <button class="preview-btn" @click="refreshPreview" title="刷新预览">
              <svg viewBox="0 0 24 24" width="14" height="14">
                <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </button>
          </div>
        </div>
        <div class="preview-content">
          <div class="preview-container">
            <div v-if="compileError" class="error-message">
              <h4>编译错误:</h4>
              <pre>{{ compileError }}</pre>
            </div>
            <div v-else-if="previewHtml" class="preview-html" v-html="previewHtml"></div>
            <div v-else class="loading-message">
              正在编译...
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'

const props = defineProps({
  initialCode: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['codeChange'])

// 编辑器状态
const currentCode = ref('')
const previewHtml = ref('')
const compileError = ref('')

// 初始化代码
const initCode = () => {
  if (props.initialCode) {
    currentCode.value = props.initialCode
  } else {
    currentCode.value = `<template>
  <div class="demo">
    <h1>Hello World</h1>
    <p>编辑代码查看实时效果</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const message = ref('Hello from Vue!')
<\/script>

<style scoped>
.demo {
  padding: 20px;
  text-align: center;
}
</style>`
  }

  // 初始编译
  compileCode()
}

// 代码变化处理
const onCodeChange = () => {
  emit('codeChange', currentCode.value)
  compileCode()
}

// 简单的代码编译预览
const compileCode = () => {
  try {
    compileError.value = ''

    if (!currentCode.value.trim()) {
      previewHtml.value = ''
      return
    }

    // 提取template内容
    const templateMatch = currentCode.value.match(/<template>([\s\S]*?)<\/template>/)
    const scriptMatch = currentCode.value.match(/<script[^>]*>([\s\S]*?)<\/script>/)
    const styleMatch = currentCode.value.match(/<style[^>]*>([\s\S]*?)<\/style>/)

    let html = templateMatch ? templateMatch[1] : '<div>No template found</div>'

    // 简单的变量替换（这里只是演示，实际应该用Vue的编译器）
    if (scriptMatch) {
      const scriptContent = scriptMatch[1]
      const refMatches = scriptContent.match(/const\s+(\w+)\s*=\s*ref\(['"`]([^'"`]*?)['"`]\)/g)
      if (refMatches) {
        refMatches.forEach(match => {
          const varMatch = match.match(/const\s+(\w+)\s*=\s*ref\(['"`]([^'"`]*?)['"`]\)/)
          if (varMatch) {
            const varName = varMatch[1]
            const varValue = varMatch[2]
            html = html.replace(new RegExp(`{{\\s*${varName}\\s*}}`, 'g'), varValue)
          }
        })
      }
    }

    // 添加样式
    if (styleMatch) {
      html = `<style>${styleMatch[1]}</style>${html}`
    }

    previewHtml.value = html
  } catch (error) {
    compileError.value = error.message
    previewHtml.value = ''
  }
}

// 运行代码
const runCode = () => {
  compileCode()
}

// 重置代码
const resetCode = () => {
  if (props.initialCode) {
    currentCode.value = props.initialCode
    compileCode()
  }
}

// 刷新预览
const refreshPreview = () => {
  compileCode()
}

// 监听代码变化
watch(() => props.initialCode, (newCode) => {
  if (newCode) {
    currentCode.value = newCode
    compileCode()
  }
})

onMounted(() => {
  initCode()
})
</script>

<style scoped>
.code-editor {
  display: flex;
  flex-direction: column;
  height: 600px;
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  overflow: hidden;
  background: var(--vp-c-bg);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-border);
}

.editor-tabs {
  display: flex;
  gap: 4px;
}

.editor-tab {
  padding: 6px 12px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  color: var(--vp-c-text-2);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.editor-tab.active {
  background: var(--vp-c-brand);
  color: white;
  border-color: var(--vp-c-brand);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.editor-btn {
  padding: 6px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  color: var(--vp-c-text-2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.editor-btn:hover {
  background: var(--vp-c-bg-mute);
  color: var(--vp-c-brand);
}

.editor-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

.editor-pane {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.monaco-editor-container {
  flex: 1;
  min-height: 0;
}

.preview-pane {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--vp-c-border);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-border);
  font-size: 12px;
  font-weight: 500;
  color: var(--vp-c-text-2);
}

.preview-actions {
  display: flex;
  gap: 4px;
}

.preview-btn {
  padding: 4px;
  background: transparent;
  border: none;
  color: var(--vp-c-text-2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  transition: all 0.2s;
}

.preview-btn:hover {
  background: var(--vp-c-bg-mute);
  color: var(--vp-c-brand);
}

.preview-content {
  flex: 1;
  position: relative;
}

.preview-iframe-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}
</style>
