<template>
  <div class="code-editor">
    <div class="editor-header">
      <div class="editor-tabs">
        <button
          v-for="file in files"
          :key="file.filename"
          class="editor-tab"
          :class="{ active: activeFile === file.filename }"
          @click="activeFile = file.filename"
        >
          {{ file.filename }}
        </button>
      </div>
      <div class="editor-actions">
        <button class="editor-btn" @click="runCode" title="运行代码">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M8 5v14l11-7z"/>
          </svg>
        </button>
        <button class="editor-btn" @click="resetCode" title="重置代码">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
          </svg>
        </button>
      </div>
    </div>

    <div class="editor-content">
      <div class="editor-pane">
        <textarea
          ref="codeTextarea"
          v-model="currentCode"
          class="code-textarea"
          :placeholder="'在这里编辑 ' + activeFile + ' 的代码...'"
          @input="onCodeChange"
        ></textarea>
      </div>

      <div class="preview-pane">
        <div class="preview-header">
          <span>预览</span>
          <div class="preview-actions">
            <button class="preview-btn" @click="refreshPreview" title="刷新预览">
              <svg viewBox="0 0 24 24" width="14" height="14">
                <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </button>
          </div>
        </div>
        <div class="preview-content">
          <div ref="previewContainer" class="preview-container">
            <component :is="compiledComponent" v-if="compiledComponent" />
            <div v-else-if="compileError" class="error-message">
              <h4>编译错误:</h4>
              <pre>{{ compileError }}</pre>
            </div>
            <div v-else class="loading-message">
              正在编译...
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import * as monaco from 'monaco-editor'

const props = defineProps({
  initialCode: {
    type: String,
    default: ''
  },
  initialFiles: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['codeChange', 'run'])

// 编辑器状态
const editorContainer = ref(null)
const previewContainer = ref(null)
const previewIframe = ref(null)
let monacoEditor = null

// 文件管理
const files = ref([])
const activeFile = ref('App.vue')

// 初始化文件
const initFiles = () => {
  if (props.initialFiles.length > 0) {
    files.value = [...props.initialFiles]
  } else if (props.initialCode) {
    files.value = [
      {
        filename: 'App.vue',
        code: props.initialCode,
        language: 'vue'
      }
    ]
  } else {
    files.value = [
      {
        filename: 'App.vue',
        code: `<template>
  <div class="demo">
    <h1>Hello World</h1>
    <p>编辑代码查看实时效果</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const message = ref('Hello from Vue!')
</script>

<style scoped>
.demo {
  padding: 20px;
  text-align: center;
}
</style>`,
        language: 'vue'
      }
    ]
  }
}

// 获取当前活动文件
const getCurrentFile = () => {
  return files.value.find(f => f.filename === activeFile.value)
}

// 初始化Monaco编辑器
const initMonacoEditor = async () => {
  if (!editorContainer.value) return

  // 配置Monaco编辑器
  monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
    target: monaco.languages.typescript.ScriptTarget.Latest,
    allowNonTsExtensions: true,
    moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
    module: monaco.languages.typescript.ModuleKind.CommonJS,
    noEmit: true,
    esModuleInterop: true,
    jsx: monaco.languages.typescript.JsxEmit.Preserve,
    reactNamespace: 'Vue',
    allowJs: true,
    typeRoots: ['node_modules/@types']
  })

  // 创建编辑器实例
  monacoEditor = monaco.editor.create(editorContainer.value, {
    value: getCurrentFile()?.code || '',
    language: getCurrentFile()?.language || 'vue',
    theme: 'vs-dark',
    fontSize: 14,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    automaticLayout: true,
    tabSize: 2,
    insertSpaces: true
  })

  // 监听代码变化
  monacoEditor.onDidChangeModelContent(() => {
    const currentFile = getCurrentFile()
    if (currentFile) {
      currentFile.code = monacoEditor.getValue()
      emit('codeChange', {
        filename: activeFile.value,
        code: currentFile.code,
        files: files.value
      })
    }
  })
}

// 切换文件
watch(activeFile, (newFile) => {
  if (monacoEditor) {
    const file = getCurrentFile()
    if (file) {
      monacoEditor.setValue(file.code)
      monaco.editor.setModelLanguage(monacoEditor.getModel(), file.language)
    }
  }
})

// 运行代码
const runCode = () => {
  emit('run', {
    files: files.value,
    activeFile: activeFile.value
  })
  refreshPreview()
}

// 重置代码
const resetCode = () => {
  const currentFile = getCurrentFile()
  if (currentFile && props.initialCode) {
    currentFile.code = props.initialCode
    if (monacoEditor) {
      monacoEditor.setValue(props.initialCode)
    }
  }
}

// 刷新预览
const refreshPreview = () => {
  if (previewIframe.value) {
    previewIframe.value.src = previewIframe.value.src
  }
}

// iframe加载完成
const onIframeLoad = () => {
  // 可以在这里注入代码到iframe
}

onMounted(async () => {
  initFiles()
  await nextTick()
  await initMonacoEditor()
})
</script>

<style scoped>
.code-editor {
  display: flex;
  flex-direction: column;
  height: 600px;
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  overflow: hidden;
  background: var(--vp-c-bg);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-border);
}

.editor-tabs {
  display: flex;
  gap: 4px;
}

.editor-tab {
  padding: 6px 12px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  color: var(--vp-c-text-2);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.editor-tab.active {
  background: var(--vp-c-brand);
  color: white;
  border-color: var(--vp-c-brand);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.editor-btn {
  padding: 6px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  color: var(--vp-c-text-2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.editor-btn:hover {
  background: var(--vp-c-bg-mute);
  color: var(--vp-c-brand);
}

.editor-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

.editor-pane {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.monaco-editor-container {
  flex: 1;
  min-height: 0;
}

.preview-pane {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--vp-c-border);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-border);
  font-size: 12px;
  font-weight: 500;
  color: var(--vp-c-text-2);
}

.preview-actions {
  display: flex;
  gap: 4px;
}

.preview-btn {
  padding: 4px;
  background: transparent;
  border: none;
  color: var(--vp-c-text-2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  transition: all 0.2s;
}

.preview-btn:hover {
  background: var(--vp-c-bg-mute);
  color: var(--vp-c-brand);
}

.preview-content {
  flex: 1;
  position: relative;
}

.preview-iframe-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}
</style>
