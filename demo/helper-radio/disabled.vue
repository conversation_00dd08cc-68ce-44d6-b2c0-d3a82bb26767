<template>
  <div>
    <HelperRadio v-model="normalValue" name="disabled-group" label="正常状态" />
    <HelperRadio v-model="disabledUnchecked" name="disabled-group" label="禁用未选中" disabled />
    <HelperRadio v-model="disabledChecked" name="disabled-group" label="禁用已选中" disabled />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import HelperRadio from 'helper-components/helper-radio/index.vue'

const normalValue = ref(false)
const disabledUnchecked = ref(false)
const disabledChecked = ref(true) // 默认选中并禁用
</script>
