<template>
  <div>
    <HelperRadio
      v-model="value1"
      name="basic-group"
      label="选项一"
      @update:modelValue="handleChange('option1', $event)"
    />
    <HelperRadio
      v-model="value2"
      name="basic-group"
      label="选项二"
      @update:modelValue="handleChange('option2', $event)"
    />
    <HelperRadio
      v-model="value3"
      name="basic-group"
      label="选项三"
      @update:modelValue="handleChange('option3', $event)"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import HelperRadio from 'helper-components/helper-radio/index.vue'

const value1 = ref(false)
const value2 = ref(false)
const value3 = ref(false)
const selectedOption = ref('')

const handleChange = (option, checked) => {
  if (checked) {
    // 单选逻辑：只能选中一个
    value1.value = option === 'option1'
    value2.value = option === 'option2'
    value3.value = option === 'option3'

    selectedOption.value = option === 'option1' ? '选项一' :
                          option === 'option2' ? '选项二' : '选项三'
  }
}
</script>
