<template>
  <div>
    <HelperRadio
      v-model="value1"
      name="custom-id-group"
      id="my-radio-1"
      label="自定义 ID: my-radio-1"
    />
    <HelperRadio
      v-model="value2"
      name="custom-id-group"
      id="my-radio-2"
      label="自定义 ID: my-radio-2"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import HelperRadio from 'helper-components/helper-radio/index.vue'

const value1 = ref(false)
const value2 = ref(false)
</script>
