<template>
  <div>
    <h4 style="margin-bottom: 16px;">用户注册表单</h4>
    
    <div style="margin-bottom: 20px;">
      <label style="display: block; margin-bottom: 8px; font-weight: 500;">性别：</label>
      <HelperRadio 
        v-model="genderMale" 
        name="gender" 
        label="男" 
        @update:modelValue="handleGenderChange('male', $event)"
      />
      <HelperRadio 
        v-model="genderFemale" 
        name="gender" 
        label="女" 
        @update:modelValue="handleGenderChange('female', $event)"
      />
    </div>

    <div style="margin-bottom: 20px;">
      <label style="display: block; margin-bottom: 8px; font-weight: 500;">通知方式：</label>
      <HelperRadio 
        v-model="notifyEmail" 
        name="notify" 
        label="邮件通知" 
        @update:modelValue="handleNotifyChange('email', $event)"
      />
      <HelperRadio 
        v-model="notifySms" 
        name="notify" 
        label="短信通知" 
        @update:modelValue="handleNotifyChange('sms', $event)"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import HelperRadio from 'helper-components/helper-radio/index.vue'

// 性别选择
const genderMale = ref(false)
const genderFemale = ref(false)
const selectedGender = ref('')

const handleGenderChange = (gender, checked) => {
  if (checked) {
    genderMale.value = gender === 'male'
    genderFemale.value = gender === 'female'
    selectedGender.value = gender === 'male' ? '男' : '女'
  }
}

// 通知方式
const notifyEmail = ref(false)
const notifySms = ref(false)
const selectedNotify = ref('')

const handleNotifyChange = (notify, checked) => {
  if (checked) {
    notifyEmail.value = notify === 'email'
    notifySms.value = notify === 'sms'
    selectedNotify.value = notify === 'email' ? '邮件通知' : '短信通知'
  }
}
</script>
