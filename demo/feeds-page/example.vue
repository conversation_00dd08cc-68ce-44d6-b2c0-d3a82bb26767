<template>
  <div class="demo-container">
    <h2>FeedsPage 组件示例</h2>
    <p class="info">
      此组件会自动检测环境。在 WeGame 客户端内，它会渲染下方的 iframe 内容；在普通浏览器中，则会显示“请在 WeGame 客户端内观看”的提示。
    </p>

    <div class="controls">
      <button @click="toggleColorMode" :disabled="!isLoaded">
        切换主题 (当前: {{ colorMode }})
      </button>
      <button @click="sendMessageToIframe" :disabled="!isLoaded">
        向 Iframe 发送消息
      </button>
    </div>

    <div class="status">
      <strong>加载与通信状态:</strong> {{ statusMessage }}
    </div>

    <!-- 组件使用区域 -->
    <div class="component-wrapper">
      <FeedsPage
        ref="feedsPageRef"
        appId="demo-app-123"
        :src="iframeSrc"
        :css-file-style="customCss"
        :color-mode="colorMode"
        :callback="handleLoadCallback"
        @complete="handleLoadCompleteEvent"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
// 1. 导入组件，注意将组件名转换为 PascalCase
import FeedsPage from 'helper-components/feeds-page/index.vue';

// 定义组件实例的 ref，用于调用其暴露的方法
const feedsPageRef = ref<InstanceType<typeof FeedsPage> | null>(null);

// 定义组件所需的响应式数据
const colorMode = ref<'light' | 'dark'>('light');
const statusMessage = ref('等待组件加载...');
const isLoaded = ref(false);

// 2. 【特性: 样式定制】准备注入 iframe 的自定义 CSS 字符串
const customCss = `
  body {
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    transition: background-color 0.3s, color 0.3s;
  }
  .message-box {
    border: 1px solid #ccc;
    padding: 15px;
    margin-top: 20px;
    border-radius: 8px;
    background-color: #f9f9f9;
  }
  /* 组件会自动为 body 添加 data-color-mode="dark" 属性 */
  body[data-color-mode="dark"] {
    background-color: #1e1e1e;
    color: #d4d4d4;
  }
  body[data-color-mode="dark"] .message-box {
    background-color: #2a2a2a;
    border-color: #444;
  }
`;

// 3. 【特性: 内容嵌入】使用 data URI 作为 iframe 的 src，使示例可独立运行
//    该 HTML 包含一个脚本，用于接收 postMessage 消息
const iframeHtmlContent = `
  <!DOCTYPE html>
  <html lang="zh-CN">
  <head><title>嵌入内容</title><meta charset="UTF-8"></head>
  <body>
    <h1>嵌入的动态页面</h1>
    <p>此页面的样式受父级传入的 <strong>cssFileStyle</strong> 和 <strong>colorMode</strong> 控制。</p>
    <div class="message-box">
      <strong>从父组件收到的消息:</strong>
      <pre id="msg" style="white-space: pre-wrap; word-break: break-all;">... 等待消息 ...</pre>
    </div>
    <script>
      window.addEventListener('message', (event) => {
        try {
          // 组件发送的是字符串化的 JSON
          const data = JSON.parse(event.data);
          const msgEl = document.getElementById('msg');
          if (msgEl) {
            msgEl.textContent = JSON.stringify(data, null, 2);
          }
        } catch (e) {
          console.error('解析消息失败:', e);
        }
      });
    <\/script>
  </body>
  </html>
`.replace(/\n\s*/g, ''); // 压缩HTML

const iframeSrc = `data:text/html;charset=utf-8,${encodeURIComponent(iframeHtmlContent)}`;

// 4. 【用法: 样式切换】定义方法来切换 colorMode
const toggleColorMode = () => {
  colorMode.value = colorMode.value === 'light' ? 'dark' : 'light';
  statusMessage.value = `主题已切换为: ${colorMode.value}`;
};

// 5. 【特性: 父子通信】定义方法，通过 ref 调用组件暴露的 postMessageToIFrame 方法
const sendMessageToIframe = () => {
  if (!feedsPageRef.value) {
    alert('组件实例不可用，可能未在WeGame环境中。');
    return;
  }
  const message = {
    type: 'user_action',
    payload: {
      action: 'show_profile',
      userId: 'wegame_user_001',
      timestamp: new Date().toISOString(),
    },
  };
  feedsPageRef.value.postMessageToIFrame(message);
  statusMessage.value = `已发送消息: ${message.type}`;
};

// 6. 【特性: 异步回调】定义用于 callback prop 和 @complete 事件的方法
const handleLoadCallback = () => {
  console.log('FeedsPage: callback 被调用');
  statusMessage.value = '组件加载完成 (通过 callback prop)';
  isLoaded.value = true;
};

const handleLoadCompleteEvent = () => {
  console.log('FeedsPage: @complete 事件被触发');
  // 两个都会触发，通常用一个即可。这里演示两者都可用。
  statusMessage.value = '组件加载完成 (通过 @complete 事件)';
  isLoaded.value = true;
};
</script>

<style scoped>
.demo-container {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  max-width: 800px;
  margin: 20px auto;
  font-family: sans-serif;
  background-color: #fff;
}
.info {
  background-color: #f0f8ff;
  border-left: 4px solid #50a0e0;
  padding: 10px 15px;
  margin-bottom: 20px;
}
.controls {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.controls button {
  padding: 8px 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f7f7f7;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}
.controls button:hover {
  background-color: #e9e9e9;
}
.controls button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.status {
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: #333;
}
.component-wrapper {
  border: 2px dashed #ccc;
  height: 450px;
  position: relative; /* 确保 feeds-page 组件能正确填充 */
  overflow: hidden;
  border-radius: 4px;
}
</style>