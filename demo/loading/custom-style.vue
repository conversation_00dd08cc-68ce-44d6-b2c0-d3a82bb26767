<template>
  <div>
    <div style="margin-bottom: 24px;">
      <div class="custom-loading-1">
        <HelperLoading>大尺寸加载</HelperLoading>
      </div>
    </div>
    
    <div style="margin-bottom: 24px;">
      <div class="custom-loading-2">
        <HelperLoading>红色主题</HelperLoading>
      </div>
    </div>
    
    <div style="margin-bottom: 24px;">
      <div class="custom-loading-3">
        <HelperLoading>快速旋转</HelperLoading>
      </div>
    </div>
    
    <p style="margin-top: 16px; color: #666;">
      通过CSS变量可以自定义加载器的尺寸、颜色和动画速度
    </p>
  </div>
</template>

<script setup>
import HelperLoading from 'helper-components/loading/index.vue'
</script>

<style scoped>
.custom-loading-1 {
  --helper-loading-width: 36px;
  --helper-loading-height: 36px;
  --helper-loading-line-width: 6px;
}

.custom-loading-2 {
  --helper-loading-line-color-current: #ff4757;
  --helper-loading-line-color-text: #ff4757;
}

.custom-loading-3 {
  --helper-loading-line-duration: 0.5s;
  --helper-loading-line-color-current: #2ed573;
  --helper-loading-line-color-text: #2ed573;
}
</style>
