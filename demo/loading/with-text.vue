<template>
  <div>
    <div style="margin-bottom: 24px;">
      <HelperLoading>加载中...</HelperLoading>
    </div>
    
    <div style="margin-bottom: 24px;">
      <HelperLoading>正在处理数据</HelperLoading>
    </div>
    
    <div style="margin-bottom: 24px;">
      <HelperLoading>请稍候</HelperLoading>
    </div>
    
    <p style="margin-top: 16px; color: #666;">
      通过插槽可以添加自定义的加载文本
    </p>
  </div>
</template>

<script setup>
import HelperLoading from 'helper-components/loading/index.vue'
</script>
