<template>
  <div class="demo-container">
    <h3>基础用法</h3>
    <div class="button-group">
      <button @click="showBasicToast">基础提示</button>
      <button @click="showSuccessToast">成功提示</button>
      <button @click="showErrorToast">失败提示</button>
      <button @click="showInfoToast">信息提示</button>
    </div>

    <h3>自定义位置</h3>
    <div class="button-group">
      <button @click="showBottomToast">底部提示</button>
    </div>

    <h3>自定义时长</h3>
    <div class="button-group">
      <button @click="showLongToast">5秒后消失</button>
    </div>

    <h3>阻止背景点击</h3>
    <div class="button-group">
      <button @click="showPreventClickToast">显示遮罩层</button>
    </div>

    <h3>使用插槽</h3>
    <div class="button-group">
      <button @click="showDefaultSlotExample">默认插槽</button>
      <button @click="showCustomSlotExample">自定义内容插槽</button>
    </div>
  </div>

  <!-- Component instance for props-based demos -->
  <HelperToast
    v-model="toastState.show"
    :text="toastState.text"
    :icon="toastState.icon"
    :position="toastState.position"
    :duration="toastState.duration"
    :prevent-click="toastState.preventClick"
  />

  <!-- Component instance for default slot demo -->
  <HelperToast v-model="showDefaultSlot" position="center" icon="success">
    <strong>操作成功!</strong>
    <p style="margin: 4px 0 0; font-size: 12px">数据已保存至服务器。</p>
  </HelperToast>

  <!-- Component instance for custom slot demo -->
  <HelperToast v-model="showCustomSlot" position="center" :prevent-click="true">
    <template #custom>
      <div class="custom-toast">
        <img
          src="https://vuejs.org/images/logo.png"
          alt="logo"
          class="custom-logo"
        />
        <h4>自定义模板</h4>
        <p>内容完全由您定义</p>
        <button class="custom-close-btn" @click="showCustomSlot = false">
          手动关闭
        </button>
      </div>
    </template>
  </HelperToast>
</template>

<script setup lang="ts">
import HelperToast from 'helper-components/helper-toast/index.vue';
import { ref, reactive } from 'vue';

// For props-based demos
const toastState = reactive({
  show: false,
  text: '',
  icon: undefined as 'info' | 'success' | 'error' | undefined,
  position: 'center' as 'center' | 'bottom' | 'auto',
  duration: 3000,
  preventClick: false,
});

// A helper function to show toast with different configurations
const showToast = (config: Partial<typeof toastState>) => {
  // Reset to default before applying new config
  Object.assign(toastState, {
    show: false,
    text: '',
    icon: undefined,
    position: 'center',
    duration: 3000,
    preventClick: false,
  });
  // Apply new config and show the toast
  Object.assign(toastState, config, { show: true });
};

const showBasicToast = () => {
  showToast({ text: '这是一条基础提示' });
};

const showSuccessToast = () => {
  showToast({ text: '操作成功', icon: 'success' });
};

const showErrorToast = () => {
  showToast({ text: '加载失败，请重试', icon: 'error' });
};

const showInfoToast = () => {
  showToast({ text: '这是一条信息提示', icon: 'info' });
};

const showBottomToast = () => {
  showToast({ text: '我出现在底部', position: 'bottom' });
};

const showLongToast = () => {
  showToast({ text: '我将显示5秒', duration: 5000, icon: 'info' });
};

const showPreventClickToast = () => {
  showToast({ text: '背景无法点击', preventClick: true, duration: 5000 });
};

// For slot-based demos
const showDefaultSlot = ref(false);
const showCustomSlot = ref(false);

const showDefaultSlotExample = () => {
  showDefaultSlot.value = true;
};

const showCustomSlotExample = () => {
  showCustomSlot.value = true;
};
</script>

<style scoped>
.demo-container {
  padding: 24px;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
}
.demo-container h3 {
  margin-top: 24px;
  margin-bottom: 12px;
  border-left: 4px solid #42b983;
  padding-left: 12px;
  font-size: 18px;
  font-weight: 600;
}
.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
button {
  padding: 8px 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  color: #606266;
}
button:hover {
  border-color: #42b983;
  color: #42b983;
}
button:active {
  background-color: #f2f8f5;
}

/* Custom Slot Styles */
.custom-toast {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: white;
  color: #333;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.custom-logo {
  width: 40px;
  height: 40px;
}
.custom-toast h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}
.custom-toast p {
  margin: 0;
  font-size: 14px;
}
.custom-close-btn {
  margin-top: 10px;
  background-color: #42b983;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
}
.custom-close-btn:hover {
  background-color: #36a476;
  color: white;
}
</style>