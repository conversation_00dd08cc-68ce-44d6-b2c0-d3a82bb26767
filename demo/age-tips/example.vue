<template>
  <div class="demo-container">
    <h3>基础用法</h3>
    <p>
      为 age-tips 组件提供一个 `gameId`。组件挂载后，将自动获取并展示适龄提醒图标。点击图标可查看详情弹窗。
    </p>
    <p>
      示例 Game ID: <strong>"a-random-game-id-12345"</strong>
    </p>
    
    <!-- 
      在实际项目中，请确保网络环境可以访问组件内部依赖的API，否则组件可能因数据获取失败而不显示。
    -->
    <AgeTips game-id="a-random-game-id-12345" />
  </div>
</template>

<script setup lang="ts">
import AgeTips from 'helper-components/age-tips/index.vue';

// 该组件的核心逻辑已在内部封装，包括API请求和弹窗管理。
// 使用时，仅需关注并传入必需的 `gameId` prop 即可。
</script>

<style scoped>
.demo-container {
  padding: 20px;
  border-radius: 6px;
  background-color: #f9f9f9;
  border: 1px solid #eee;
}

h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}

p {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16px;
}
</style>