<template>
  <div class="audio-player-demo-container">
    <!-- 
      这是一个完整的音频播放器实现。
      它通过组合 Lyric, AudioList, 和 AudioCtrlBar 三个子组件来工作。
      父组件（即当前文件）负责核心状态管理和 <audio> 元素的控制，
      子组件则负责UI展示和用户交互事件的派发。
    -->

    <!-- 1. 歌词显示区域 -->
    <Lyric
      class="lyric-view"
      :url="currentAudio?.lrc"
      :current-time="currentTime"
    />

    <!-- 2. 播放列表区域 -->
    <AudioList
      class="list-view"
      :audio-list="audioList"
      :audio="currentAudio"
      :is-playing="isPlaying"
      @play="handlePlay"
    />

    <!-- 3. 固定在底部的控制条 -->
    <div class="control-bar-placeholder"></div>
    <div class="control-bar-container">
      <AudioCtrlBar
        v-if="currentAudio"
        :audio="currentAudio"
        :is-playing="isPlaying"
        :current-time="currentTime"
        :duration="duration"
        :audio-time-text="audioTimeText"
        :is-error="isError"
        error-txt="音频加载失败"
        @play="handlePlay"
        @pause="handlePause"
        @set-current-time="handleSetCurrentTime"
      />
    </div>

    <!-- 
      隐藏的 HTML5 <audio> 元素，是所有播放逻辑的核心。
      我们通过 Vue 的 ref 来控制它。
    -->
    <audio
      ref="audioRef"
      :src="currentAudio?.url"
      @timeupdate="onTimeUpdate"
      @loadedmetadata="onLoadedMetadata"
      @ended="playNext"
      @error="onPlayError"
      @play="onPlay"
      @pause="onPause"
    ></audio>
  </div>
</template>

<script setup lang="ts">
// 导入 Vue 的核心功能
import { ref, computed, watch } from 'vue';

// 导入音频播放器的三个UI子组件
// 注意：在实际项目中，请确保路径正确。
// 这里我们遵循示例要求，但实际使用时你可能需要调整路径。
// import AudioPlayer from 'helper-components/audio-player/index.vue' 
// 上述导入不适用，因为提供了三个分离的组件。我们分别导入它们。
import AudioCtrlBar from 'helper-components/audio-player/audio-ctrl-bar.vue';
import AudioList from 'helper-components/audio-player/audio-list.vue';
import Lyric from 'helper-components/audio-player/lyric.vue';

// --- 类型定义 ---

interface AudioItem {
  url: string; // 音频文件URL
  lrc?: string; // LRC歌词文件URL
  title: string; // 歌曲标题
  artist: string; // 艺术家
  albumCover: string; // 专辑封面URL
  duration: string; // 格式化的时长字符串（用于列表显示）
}

// --- 辅助函数 ---

/**
 * 创建一个包含LRC歌词内容的Blob URL。
 * 这使得我们无需外部.lrc文件即可测试歌词功能。
 * @param lrcContent - LRC格式的字符串
 * @returns 可在 <Lyric> 组件中使用的URL
 */
const createLrcBlobUrl = (lrcContent: string): string => {
  const blob = new Blob([lrcContent.trim()], { type: 'text/plain' });
  return URL.createObjectURL(blob);
};

/**
 * 格式化秒数为 mm:ss 格式的字符串。
 * @param seconds - 总秒数
 * @returns 格式化后的时间字符串
 */
const formatTime = (seconds: number): string => {
  if (isNaN(seconds) || seconds < 0) return '00:00';
  const min = Math.floor(seconds / 60);
  const sec = Math.floor(seconds % 60);
  return `${min.toString().padStart(2, '0')}:${sec.toString().padStart(2, '0')}`;
};


// --- 测试数据 ---

const audioList = ref<AudioItem[]>([
  {
    url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
    lrc: createLrcBlobUrl(`
      [ti:SoundHelix Song 1]
      [ar:SoundHelix]
      [al:Examples]
      [00:01.00]This is the first line of the lyrics.
      [00:05.50]Welcome to the audio player demo.
      [00:10.20]The current time is being tracked.
      [00:15.80]And the lyric will scroll automatically.
      [00:20.00]...
      [00:25.30]This component supports LRC format.
      [00:30.10]Each line is timed.
      [00:35.60]Enjoy the music!
      [00:40.00]...
      [00:45.00]Scrolling to the center.
      [00:50.00]Line by line.
      [00:55.00]The end of the sample lyrics.
    `),
    title: 'SoundHelix Song 1',
    artist: 'SoundHelix',
    albumCover: 'https://picsum.photos/seed/song1/100/100',
    duration: '05:54',
  },
  {
    url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-8.mp3',
    lrc: createLrcBlobUrl(`
      [ti:Pure Music Example]
      [ar:Unknown]
      [al:No Lyrics]
    `),
    title: 'Pure Music (No Lyrics)',
    artist: 'SoundHelix',
    albumCover: 'https://picsum.photos/seed/song2/100/100',
    duration: '04:03',
  },
  {
    url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-15.mp3',
    lrc: createLrcBlobUrl(`
      [ti:Another Track]
      [ar:Demo Artist]
      [al:Vue Demo Album]
      [00:02.50]This is another song.
      [00:07.00]With its own set of lyrics.
      [00:12.00]Click on the playlist to switch.
      [00:17.50]The player state will update.
      [00:22.00]Including the lyric view.
    `),
    title: 'SoundHelix Song 15',
    artist: 'Demo Artist',
    albumCover: 'https://picsum.photos/seed/song3/100/100',
    duration: '03:13',
  },
]);

// --- 响应式状态管理 ---

const audioRef = ref<HTMLAudioElement | null>(null); // <audio> 元素引用
const currentAudio = ref<AudioItem | null>(audioList.value[0]); // 当前播放的音频对象
const isPlaying = ref(false); // 是否正在播放
const currentTime = ref(0); // 当前播放时间（秒）
const duration = ref(0); // 音频总时长（秒）
const isError = ref(false); // 是否发生播放错误

// --- 计算属性 ---

// 格式化 "当前时间 / 总时长" 的文本
const audioTimeText = computed(() => {
  return `${formatTime(currentTime.value)} / ${formatTime(duration.value)}`;
});

// 当前播放歌曲在列表中的索引
const currentAudioIndex = computed(() => {
  if (!currentAudio.value) return -1;
  return audioList.value.findIndex(item => item.url === currentAudio.value!.url);
});


// --- 事件处理器 ---

/**
 * 处理来自 AudioList 或 AudioCtrlBar 的播放请求
 * @param payload - 包含要播放的音频对象 { audio: AudioItem }
 */
const handlePlay = (payload: { audio: AudioItem }) => {
  // 如果点击的是当前已选中的歌曲，则继续播放/暂停切换
  if (currentAudio.value?.url === payload.audio.url) {
    if (audioRef.value) {
        audioRef.value.play();
    }
  } else {
    // 否则，切换到新歌曲
    currentAudio.value = payload.audio;
    // Vue 会在 src 变化后自动加载新音频，我们监听 canplay 事件或直接 play
    // 为了更快的响应，我们使用 watch 监听 currentAudio 的变化来播放
  }
};

/**
 * 处理来自 AudioCtrlBar 的暂停请求
 */
const handlePause = () => {
  audioRef.value?.pause();
};

/**
 * 处理来自 AudioCtrlBar 的拖拽/点击进度条事件
 * @param payload - 包含目标时间 { time: number }
 */
const handleSetCurrentTime = (payload: { time: number }) => {
  if (audioRef.value) {
    audioRef.value.currentTime = payload.time;
    // 如果是暂停状态下拖动，拖动后开始播放
    if (audioRef.value.paused) {
        audioRef.value.play();
    }
  }
};

/**
 * 播放下一首歌曲
 */
const playNext = () => {
  const nextIndex = (currentAudioIndex.value + 1) % audioList.value.length;
  currentAudio.value = audioList.value[nextIndex];
};

// --- <audio> 元素的原生事件监听 ---

const onTimeUpdate = () => {
  if (audioRef.value) {
    currentTime.value = audioRef.value.currentTime;
  }
};

const onLoadedMetadata = () => {
  if (audioRef.value) {
    duration.value = audioRef.value.duration;
  }
};

const onPlayError = () => {
  isError.value = true;
  isPlaying.value = false;
};

const onPlay = () => {
    isPlaying.value = true;
    isError.value = false;
}

const onPause = () => {
    isPlaying.value = false;
}

// --- 侦听器 ---

// 当 currentAudio 改变时，自动播放新歌曲
watch(currentAudio, async (newAudio) => {
  if (newAudio && audioRef.value) {
    // 等待DOM更新，audio的src已改变
    await nextTick();
    audioRef.value.play().catch(onPlayError);
  }
});
</script>

<style scoped>
/* Demo容器样式 */
.audio-player-demo-container {
  width: 100%;
  max-width: 420px;
  height: 80vh;
  max-height: 750px;
  margin: 5vh auto;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
  overflow: hidden;
  position: relative;
}

/* 歌词区域样式 */
.lyric-view {
  flex-shrink: 0;
  height: 200px;
  padding: 20px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

/* 列表区域样式 */
.list-view {
  flex-grow: 1;
  overflow-y: auto;
}

/* 控制条容器样式 */
.control-bar-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-top: 1px solid #eee;
  padding: 10px 15px;
  z-index: 10;
}

/* 控制条占位，防止列表内容被遮挡 */
.control-bar-placeholder {
  flex-shrink: 0;
  /* 高度约等于控制条的高度 */
  height: 90px; 
}

/* 美化滚动条 */
.list-view::-webkit-scrollbar {
  width: 6px;
}

.list-view::-webkit-scrollbar-track {
  background: transparent;
}

.list-view::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 10px;
}
</style>