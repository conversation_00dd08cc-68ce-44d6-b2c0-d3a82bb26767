# helper-button 按钮

::: tip 组件介绍
按钮用于触发一个操作，如提交表单、打开对话框、取消操作或执行删除操作等。
:::

## 页面模版

<demo src="helper-button/example.vue"/>

## 基础用法

基础的按钮用法,通过 `type` 属性可以设置按钮的类型，可选值为 `primary`、`success`、`warning`、`danger`、`info`、`text`。

```vue
<template>
  <div class="button-demo">
    <HelperButton>默认按钮</HelperButton>
    <HelperButton type="primary">主要按钮</HelperButton>
    <HelperButton type="success">成功按钮</HelperButton>
    <HelperButton type="info">信息按钮</HelperButton>
    <HelperButton type="warning">警告按钮</HelperButton>
    <HelperButton type="danger">危险按钮</HelperButton>
  </div>
</template>
<script setup>
import HelperButton from 'helper-components/helper-button/index.vue'
</script>
<style scoped>
.button-demo .helper-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
```

## 禁用状态

通过 `disabled` 属性可以禁用按钮。禁用状态下按钮不可点击，用于表示当前操作不可用。

```vue
<template>
  <div class="button-demo">
    <HelperButton disabled>默认按钮</HelperButton>
    <HelperButton type="primary" disabled>主要按钮</HelperButton>
    <HelperButton type="success" disabled>成功按钮</HelperButton>
    <HelperButton type="info" disabled>信息按钮</HelperButton>
    <HelperButton type="warning" disabled>警告按钮</HelperButton>
    <HelperButton type="danger" disabled>危险按钮</HelperButton>
  </div>
</template>
<script setup>
import HelperButton from 'helper-components/helper-button/index.vue'
</script>
<style scoped>
.button-demo .helper-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
```

## 文字按钮

`type` 属性设置为 `text` 时，按钮将没有边框和背景色。

```vue
<template>
  <div class="button-demo">
    <HelperButton type="text">文字按钮</HelperButton>
    <HelperButton type="text" disabled>文字按钮</HelperButton>
  </div>
</template>
<script setup>
import HelperButton from 'helper-components/helper-button/index.vue'
</script>
<style scoped>
.button-demo .helper-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
```

## 图标按钮

通过`icon` 属性可以在按钮上显示图标。

```vue
<template>
  <div class="button-demo">
    <HelperButton type="primary" icon="edit">编辑</HelperButton>
    <HelperButton type="primary" icon="share">分享</HelperButton>
    <HelperButton type="primary" icon="delete">删除</HelperButton>
    <HelperButton type="primary" icon="search" circle></HelperButton>
    <HelperButton type="success" icon="edit" circle></HelperButton>
    <HelperButton type="info" icon="message" circle></HelperButton>
    <HelperButton type="warning" icon="star" circle></HelperButton>
    <HelperButton type="danger" icon="delete" circle></HelperButton>
  </div>
</template>
<script setup>
import HelperButton from 'helper-components/helper-button/index.vue'
</script>
<style scoped>
.button-demo .helper-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
```

## 加载中

通过 `loading` 属性可以显示按钮的加载状态。

```vue
<template>
  <div class="button-demo">
    <HelperButton type="primary" :loading="loading" @click="handleClick">
      点击加载
    </HelperButton>
    <HelperButton type="primary" loading>
      加载中
    </HelperButton>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import HelperButton from 'helper-components/helper-button/index.vue'
const loading = ref(false)
const handleClick = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 2000)
}
</script>
<style scoped>
.button-demo .helper-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
```

## 不同尺寸

通过`size` 属性可以设置按钮的尺寸，可选值为 `large`、`default`、`small`、`mini`。

```vue
<template>
  <div class="button-demo">
    <HelperButton size="large">大型按钮</HelperButton>
    <HelperButton>默认按钮</HelperButton>
    <HelperButton size="small">小型按钮</HelperButton>
    <HelperButton size="mini">超小按钮</HelperButton>
  </div>

  <div class="button-demo">
    <HelperButton size="large" round>大型按钮</HelperButton>
    <HelperButton round>默认按钮</HelperButton>
    <HelperButton size="small" round>小型按钮</HelperButton>
    <HelperButton size="mini" round>超小按钮</HelperButton>
  </div>
</template>
<script setup>
import HelperButton from 'helper-components/helper-button/index.vue'
</script>
<style scoped>
.button-demo {
  margin-bottom: 20px;
}
.button-demo .helper-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
```

## API

### Button Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| size | 尺寸 | string | large / default / small / mini | default |
| type | 类型 | string | primary / success / warning / danger / info / text | default |
| plain | 是否朴素按钮 | boolean | — | false |
| round | 是否圆角按钮 | boolean | — | false |
| circle | 是否圆形按钮 | boolean | — | false |
| loading | 是否加载中状态 | boolean | — | false |
| disabled | 是否禁用状态 | boolean | — | false |
| icon | 图标类名 | string | — | — |
| autofocus | 是否默认聚焦 | boolean | — | false |
| native-type | 原生 type 属性 | string | button / submit / reset | button |

### Button Events

| 事件名称 | 说明 | 回调参数 |
|----------|------|----------|
| click | 点击时触发 | (event: Event) |
| focus | 获得焦点时触发 | (event: Event) |
| blur | 失去焦点时触发 | (event: Event) |

### Button Slots

| 插槽名称 | 说明 |
|----------|------|
| default | 按钮内容 |

## 使用指南

::: warning 注意事项
1. 该组件用于触发一个操作，如提交表单、打开对话框、取消操作或执行删除操作等。
2. 在使用时，请根据不同的操作类型选择合适的按钮类型，如主要操作使用 `primary` 类型，次要操作使用默认类型，危险操作使用 `danger` 类型等。
3. 在按钮上添加图标可以更直观地传达操作含义，但不要滥用图标，以免造成混淆。
:::

::: tip 最佳实践
- **主要操作**：一个页面最多使用一个 `primary` 按钮
- **按钮文案**：使用动词开头，明确告诉用户点击后会发生什么
- **加载状态**：对于需要等待的操作，及时显示加载状态
- **响应式设计**：在移动端确保按钮触摸目标足够大（建议最小 44px）
:::

## 常见问题

### Q: 如何实现按钮的防抖功能？

可以结合 loading 状态实现：

```vue
<template>
  <HelperButton :loading="loading" @click="handleSubmit">
    提交
  </HelperButton>
</template>

<script setup>
import { ref } from 'vue'

const loading = ref(false)

const handleSubmit = async () => {
  if (loading.value) return

  loading.value = true
  try {
    await submitData()
  } finally {
    loading.value = false
  }
}
</script>
```
