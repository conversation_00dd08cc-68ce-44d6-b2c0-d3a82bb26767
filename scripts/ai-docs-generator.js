import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import axios from 'axios'
import { AI_CONFIG, PROMPTS, GENERATION_RULES } from './ai-config.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * AI 文档生成器
 */
class AIDocsGenerator {
  constructor(options = {}) {
    this.options = {
      force: false,
      component: null,
      demoOnly: false,
      ...options
    }
    this.aiClient = this.createAIClient()
  }

  /**
   * 创建AI客户端
   */
  createAIClient() {
    const headers = {
      'Content-Type': 'application/json'
    }
    
    // Ollama不需要API密钥，其他服务需要
    if (AI_CONFIG.api.key && !AI_CONFIG.api.baseURL.includes('11434')) {
      headers['Authorization'] = `Bearer ${AI_CONFIG.api.key}`
    }
    
    return axios.create({
      baseURL: AI_CONFIG.api.baseURL,
      timeout: AI_CONFIG.api.timeout,
      headers
    })
  }

  /**
   * 主执行函数
   */
  async run() {
    console.log('🚀 开始AI文档生成...')
    
    try {
      // 检查AI配置
      this.validateAIConfig()
      
      // 获取组件列表
      const components = this.getComponentList()
      console.log(`📦 发现 ${components.length} 个组件`)
      
      // 生成文档
      for (const component of components) {
        await this.generateComponentDocs(component)
      }
      
      console.log('✅ AI文档生成完成！')
    } catch (error) {
      console.error('❌ AI文档生成失败：', error.message)
      process.exit(1)
    }
  }

  /**
   * 验证AI配置
   */
  validateAIConfig() {
    // Ollama不需要API密钥，其他服务需要
    if (!AI_CONFIG.api.baseURL.includes('11434') && !AI_CONFIG.api.key) {
      throw new Error('请在环境变量中设置 AI_API_KEY')
    }
    console.log(`🤖 使用AI模型: ${AI_CONFIG.api.model}`)
    console.log(`🔗 API地址: ${AI_CONFIG.api.baseURL}`)
  }

  /**
   * 获取组件列表
   */
  getComponentList() {
    const helperComponentsPath = path.resolve(__dirname, '../helper-components')
    const items = fs.readdirSync(helperComponentsPath, { withFileTypes: true })

    let components = items
      .filter(item => item.isDirectory())
      .filter(item => this.hasVueFiles(path.join(helperComponentsPath, item.name)))
      .map(item => item.name)

    // 如果指定了组件名称，只处理该组件
    if (this.options.component) {
      components = components.filter(name => name === this.options.component)
      if (components.length === 0) {
        throw new Error(`组件 ${this.options.component} 不存在`)
      }
    }

    return components
  }

  /**
   * 检查目录下是否有Vue文件
   */
  hasVueFiles(dirPath) {
    try {
      const files = fs.readdirSync(dirPath)
      return files.some(file => file.endsWith('.vue'))
    } catch (error) {
      // 如果读取目录失败，认为不是有效组件
      return false
    }
  }

  /**
   * 生成组件文档
   */
  async generateComponentDocs(componentName) {
    console.log(`\n🔍 正在处理组件: ${componentName}`)
    
    try {
      // 1. 分析组件源码
      const componentInfo = await this.analyzeComponent(componentName)
      // 2. 检查是否需要跳过
      if (!this.options.force && this.isDocExists(componentName)) {
        console.log(`⏭️  组件 ${componentName} 文档已存在，跳过（使用 --force 强制重新生成）`)
        return
      }
      
      // 3. 调用AI生成内容
      const aiResults = await this.callAIGeneration(componentInfo)

      // 检查AI调用是否成功
      if (!aiResults) {
        console.log(`⚠️  组件 ${componentName} AI生成失败，跳过`)
        return
      }

      // 4. 创建文档目录
      this.createDirectories(componentName)

      // 5. 生成文档文件
      if (!this.options.demoOnly) {
        await this.generateDocFile(componentName, aiResults.documentation)
      }

      // 6. 生成Demo文件
      if (GENERATION_RULES.DOC_OPTIONS.includeDemo) {
        await this.generateDemoFiles(componentName, aiResults.demos)
      }
      
      // 7. 更新侧边栏配置
      if (GENERATION_RULES.DOC_OPTIONS.autoUpdateSidebar) {
        this.updateSidebarConfig(componentName)
      }
      
      console.log(`✅ 组件 ${componentName} 文档生成完成`)
      
    } catch (error) {
      console.error(`❌ 生成组件 ${componentName} 文档失败:`, error.message)
      // 继续处理其他组件，不中断整个流程
    }
  }

  /**
   * 分析组件源码
   */
  async analyzeComponent(componentName) {
    const componentPath = path.resolve(__dirname, '../helper-components', componentName)
    
    let componentInfo = {
      name: componentName,
      displayName: componentName,
      path: componentPath,
      description: '',
      props: [],
      events: [],
      slots: [],
      hasAssets: false,
      hasApi: false,
      sourceCode: {},
      interfaceCode: ''
    }
    
    if (!fs.existsSync(componentPath)) {
      throw new Error(`组件目录不存在: ${componentPath}`)
    }
    
    // 读取组件源码
    componentInfo.sourceCode = this.readComponentSource(componentPath)
    componentInfo.interfaceCode = this.readInterfaceSource(componentPath)
    
    // 检查资源
    componentInfo.hasAssets = fs.existsSync(path.join(componentPath, 'assets'))
    componentInfo.hasApi = fs.existsSync(path.join(componentPath, 'api.ts'))
    
    // 解析组件信息（基础解析）
    componentInfo = this.parseComponentInfo(componentInfo)
    
    return componentInfo
  }

  /**
   * 读取组件源码
   */
  readComponentSource(componentPath) {
    const sourceFiles = {}
    try {
      // 读取组件目录下的所有Vue文件
      const files = fs.readdirSync(componentPath)
      for (const file of files) {
        if (file.endsWith('.vue')) {
          const filePath = path.join(componentPath, file)
          const content = fs.readFileSync(filePath, 'utf-8')
          sourceFiles[file] = content
        }
      }
    } catch (error) {
      console.warn(`读取组件目录失败: ${error.message}`)
    }
    return sourceFiles
  }

  /**
   * 将对象格式的源码转换为AI可读的字符串格式
   */
  formatSourceCodeForAI(sourceFiles) {
    if (Object.keys(sourceFiles).length === 0) {
      return ''
    }
    if (Object.keys(sourceFiles).length === 1) {
      // 只有一个文件，直接返回内容
      return Object.values(sourceFiles)[0]
    }
    // 多个文件，组合成带标注的格式
    let combinedSource = ''
    for (const [fileName, content] of Object.entries(sourceFiles)) {
      combinedSource += `// 文件: ${fileName}\n${content}\n\n`
    }
    return combinedSource.trim()
  }

  /**
   * 读取文档模板作为参考
   */
  loadDocumentTemplate() {
    try {
      const templatePath = path.resolve(__dirname, 'docs-template/template.md')
      if (fs.existsSync(templatePath)) {
        const templateContent = fs.readFileSync(templatePath, 'utf-8')
        console.log('📋 已加载模板文件')
        return templateContent
      } else {
        console.warn('⚠️  文档模板不存在，将使用默认格式')
        return ''
      }
    } catch (error) {
      console.warn(`⚠️  读取文档模板失败: ${error.message}`)
      return ''
    }
  }

  /**
   * 读取接口定义
   */
  readInterfaceSource(componentPath) {
    const interfacePath = path.join(componentPath, 'interface.ts')
    if (fs.existsSync(interfacePath)) {
      return fs.readFileSync(interfacePath, 'utf-8')
    }
    return ''
  }

  /**
   * 解析组件信息（基础解析）
   */
  parseComponentInfo(componentInfo) {
    // 这里可以添加基础的代码解析逻辑
    // 提取 props、events、slots 等信息
    
    // 简单的 props 提取（可以扩展为更复杂的AST解析）
    // 只从 index.vue 文件中提取 props 信息
    const propsRegex = /defineProps<[^{]*{([^}]+)}/gs
    const indexVueContent = componentInfo.sourceCode['index.vue']

    if (indexVueContent) {
      const propsMatch = propsRegex.exec(indexVueContent)
      if (propsMatch) {
        componentInfo.props = this.parsePropsFromTypeScript(propsMatch[1])
      }
    }
    return componentInfo
  }

  /**
   * 从TypeScript代码中解析Props
   */
  parsePropsFromTypeScript(propsContent) {
    const props = []
    const lines = propsContent.split('\n')
    
    for (let line of lines) {
      line = line.trim()
      if (!line || line.startsWith('//') || line.startsWith('*')) continue
      
      const propMatch = line.match(/(\w+)(\?)?\s*:\s*([^;,\n]+)/)
      if (propMatch) {
        const [, name, optional, type] = propMatch
        props.push({
          name,
          type: type.trim(),
          required: !optional,
          description: '',
          default: optional ? 'undefined' : '-'
        })
      }
    }
    
    return props
  }

  /**
   * 调用AI生成内容
   */
  async callAIGeneration(componentInfo) {
    console.log('🤖 正在调用AI...')

    try {
      // 将对象格式的sourceCode转换为字符串格式传给AI
      const sourceCodeString = this.formatSourceCodeForAI(componentInfo.sourceCode)

      // 读取标准文档模板作为参考
      const templateReference = this.loadDocumentTemplate()

      console.log('🤖 AI正在分析组件...')
      // 1. 分析组件
      const analysis = await this.callAI(PROMPTS.ANALYZE_COMPONENT, {
        componentCode: sourceCodeString,
        interfaceCode: componentInfo.interfaceCode
      })
      console.log('🤖 AI正在生成文档...')
      // 2. 生成文档
      const documentation = await this.callAI(PROMPTS.GENERATE_DOCS, {
        componentName: componentInfo.name,
        displayName: componentInfo.displayName,
        analysis: JSON.stringify(analysis),
        props: JSON.stringify(componentInfo.props),
        events: JSON.stringify(componentInfo.events),
        slots: JSON.stringify(componentInfo.slots),
        sourceCode: sourceCodeString,
        templateReference: templateReference
      })
      console.log('🤖 AI正在生成Demo...')
      // 3. 生成Demo
      const demos = await this.callAI(PROMPTS.GENERATE_DEMO, {
        componentName: componentInfo.name,
        componentPath: componentInfo.name,
        props: JSON.stringify(componentInfo.props),
        analysis: JSON.stringify(analysis),
        sourceCode: sourceCodeString
      })
      
      return {
        analysis,
        documentation,
        demos
      }
    } catch (error) {
      console.error('错误详情:', error.response?.data || error.stack)
      return null // 返回null表示失败
    }
  }

  /**
   * 调用AI API
   */
  async callAI(prompt, variables) {
    // 替换变量
    let processedPrompt = prompt
    for (const [key, value] of Object.entries(variables)) {
      processedPrompt = processedPrompt.replace(new RegExp(`{${key}}`, 'g'), value)
    }
    
    const response = await this.aiClient.post('/chat/completions', {
      model: AI_CONFIG.api.model,
      messages: [
        {
          role: 'user',
          content: processedPrompt
        }
      ],
      temperature: AI_CONFIG.generation.temperature,
      max_tokens: AI_CONFIG.generation.maxTokens,
      top_p: AI_CONFIG.generation.topP
    })
    
    const content = response.data.choices[0].message.content
    
    // 尝试解析JSON响应
    try {
      return JSON.parse(content)
    } catch {
      return content
    }
  }

  /**
   * 检查文档是否存在
   */
  isDocExists(componentName) {
    const docPath = path.resolve(__dirname, '../docs/components', `${componentName}.md`)
    return fs.existsSync(docPath)
  }

  /**
   * 创建目录
   */
  createDirectories(componentName) {
    const docsDir = path.resolve(__dirname, '../docs/components')
    const demosDir = path.resolve(__dirname, '../demo', componentName)

    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true })
    }

    if (!fs.existsSync(demosDir)) {
      fs.mkdirSync(demosDir, { recursive: true })
    }
  }

  /**
   * 生成文档文件
   */
  async generateDocFile(componentName, documentation) {
    const docPath = path.resolve(__dirname, '../docs/components', `${componentName}.md`)

    // 清理AI生成的文档内容
    const cleanedDocumentation = documentation

    fs.writeFileSync(docPath, cleanedDocumentation)
    console.log(`📝 完成本地写入文档: ${docPath}`)
  }

  /**
   * 清理AI生成的内容，移除Markdown代码块标记和AI回复前缀
   */
  cleanAIGeneratedDemoContent(content) {
    if (typeof content !== 'string') {
      return content
    }

    // 检查是否包含代码块（支持vue、html、javascript、css等）
    const codeBlockMatch = content.match(/```(\w+)?\s*\n([\s\S]*?)(?:\n```|$)/i)
    if (codeBlockMatch) {
      // 提取代码块内容，如果没有结尾```则提取到文本结束
      content = codeBlockMatch[2]
    } 
    // 移除多余的空行
    content = content.replace(/^\n+/, '')
    content = content.replace(/\n+$/, '\n')
    return content
  }

  /**
   * 生成Demo文件
   */
  async generateDemoFiles(componentName, demos) {
    const demosDir = path.resolve(__dirname, '../demo', componentName)

    // 清理AI生成的内容
    const cleanedDemos = this.cleanAIGeneratedDemoContent(demos)

    // 生成示例Demo
    const exampleDemoPath = path.join(demosDir, 'example.vue')
    fs.writeFileSync(exampleDemoPath, cleanedDemos)
    console.log(`🎨 完成本地写入demo文件: ${exampleDemoPath}`)
  }

  /**
   * 更新侧边栏配置
   */
  updateSidebarConfig(componentName) {
    // 这里可以添加更新VitePress侧边栏配置的逻辑
    console.log(`📋 已更新侧边栏配置: ${componentName}`)
  }


}

/**
 * 解析命令行参数
 */
function parseArgs() {
  const args = process.argv.slice(2)
  const options = {}

  for (let i = 0; i < args.length; i++) {
    const arg = args[i]

    if (arg === '--force') {
      options.force = true
    } else if (arg === '--demo-only') {
      options.demoOnly = true
    } else if (arg === '--component') {
      options.component = args[++i]
    } else if (arg.startsWith('--component=')) {
      options.component = arg.split('=')[1]
    }
  }

  return options
}

/**
 * 主函数
 */
async function main() {
  const options = parseArgs()
  const generator = new AIDocsGenerator(options)
  await generator.run()
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export { AIDocsGenerator } 
