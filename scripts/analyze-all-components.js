import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import generateComponentDocs from './generate-component-docs.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * 扫描并分析所有组件
 */
async function analyzeAllComponents() {
  console.log('🔍 开始扫描所有组件...')
  
  const helperComponentsPath = path.resolve(__dirname, '../helper-components')
  
  if (!fs.existsSync(helperComponentsPath)) {
    console.error('❌ helper-components 目录不存在')
    console.log('请确保 helper-components 项目在正确的位置')
    process.exit(1)
  }
  
  // 读取所有目录
  const items = fs.readdirSync(helperComponentsPath, { withFileTypes: true })
  const componentDirs = items
    .filter(item => item.isDirectory())
    .filter(item => !item.name.startsWith('.') && !['node_modules', 'dist'].includes(item.name))
    .map(item => item.name)
  
  console.log(`📦 发现 ${componentDirs.length} 个组件目录:`)
  componentDirs.forEach(dir => console.log(`  - ${dir}`))
  
  // 分类组件
  const components = {
    valid: [],
    invalid: [],
    existing: []
  }
  
  for (const componentName of componentDirs) {
    const componentPath = path.join(helperComponentsPath, componentName)
    const docPath = path.join(__dirname, '../docs/components', `${componentName}.md`)
    
    // 检查是否是有效的组件目录
    const hasVueFile = fs.existsSync(path.join(componentPath, 'index.vue'))
    const hasIndexTs = fs.existsSync(path.join(componentPath, 'index.ts'))
    
    if (hasVueFile || hasIndexTs) {
      if (fs.existsSync(docPath)) {
        components.existing.push(componentName)
      } else {
        components.valid.push(componentName)
      }
    } else {
      components.invalid.push(componentName)
    }
  }
  
  console.log('\n📊 组件分析结果:')
  console.log(`  ✅ 有效组件 (需生成文档): ${components.valid.length}`)
  console.log(`  📄 已有文档: ${components.existing.length}`)
  console.log(`  ❌ 无效目录: ${components.invalid.length}`)
  
  if (components.invalid.length > 0) {
    console.log('\n⚠️  无效目录 (缺少主要文件):')
    components.invalid.forEach(name => console.log(`  - ${name}`))
  }
  
  if (components.existing.length > 0) {
    console.log('\n📄 已有文档的组件:')
    components.existing.forEach(name => console.log(`  - ${name}`))
  }
  
  // 生成文档
  if (components.valid.length > 0) {
    console.log(`\n🚀 开始为 ${components.valid.length} 个组件生成文档...`)
    
    let successCount = 0
    let failureCount = 0
    const failures = []
    
    for (const componentName of components.valid) {
      try {
        console.log(`\n📝 正在处理: ${componentName}`)
        await generateComponentDocs(componentName)
        successCount++
      } catch (error) {
        console.error(`❌ ${componentName} 失败:`, error.message)
        failureCount++
        failures.push({ componentName, error: error.message })
      }
    }
    
    console.log('\n🎯 生成结果:')
    console.log(`  ✅ 成功: ${successCount}`)
    console.log(`  ❌ 失败: ${failureCount}`)
    
    if (failures.length > 0) {
      console.log('\n💥 失败详情:')
      failures.forEach(({ componentName, error }) => {
        console.log(`  - ${componentName}: ${error}`)
      })
    }
  } else {
    console.log('\n✨ 所有组件都已有文档!')
  }
  
  // 生成组件总览页面
  await generateComponentsIndex(components)
  
  console.log('\n🎉 所有任务完成!')
}

/**
 * 生成组件总览页面
 * @param {Object} components 组件分类信息
 */
async function generateComponentsIndex(components) {
  console.log('\n📋 生成组件总览页面...')
  
  const allComponents = [...components.valid, ...components.existing]

  // 整理组件列表
  const componentList = allComponents.map(componentName => ({
    name: componentName,
    displayName: componentName,
    description: `${componentName}组件`
  }))
  
  const indexContent = `# 组件总览

Helper Components 为 WeGame 项目提供了丰富的组件库，包含 ${allComponents.length} 个组件。

## 组件列表

<div class="component-grid">
${componentList.map(item => `
<div class="component-card">
  <h4><a href="/docs/components/${item.name}">${item.displayName}</a></h4>
  <p>${item.description}</p>
</div>
`).join('')}
</div>

## 使用统计

- **总组件数**: ${allComponents.length}
- **最新更新**: ${new Date().toLocaleDateString('zh-CN')}

## 贡献指南

如果你想为组件库添加新组件或改进现有组件，请查看我们的 [开发指南](/guide/development)。

<style>
.component-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin: 20px 0;
}

.component-card {
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  padding: 16px;
  background-color: var(--vp-c-bg-soft);
  transition: transform 0.2s, box-shadow 0.2s;
}

.component-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.component-card h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.component-card h4 a {
  color: var(--vp-c-brand-1);
  text-decoration: none;
}

.component-card h4 a:hover {
  text-decoration: underline;
}

.component-card p {
  margin: 0;
  font-size: 14px;
  color: var(--vp-c-text-2);
  line-height: 1.5;
}
</style>`

  const indexPath = path.join(__dirname, '../components/index.md')
  fs.writeFileSync(indexPath, indexContent)
  
  console.log('✅ 组件总览页面生成完成')
}



// 命令行调用
if (process.argv[1] === __filename) {
  analyzeAllComponents()
    .catch(err => {
      console.error('💥 分析失败:', err)
      process.exit(1)
    })
}

export { analyzeAllComponents } 