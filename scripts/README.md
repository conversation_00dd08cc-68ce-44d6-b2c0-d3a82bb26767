# AI 文档生成器使用指南

## 概述

本项目提供了基于 AI 的组件文档自动生成工具，可以自动分析 Vue 组件源码并生成高质量的中文技术文档和示例代码。

## 功能特性

- 🤖 **AI 智能分析**：自动分析 Vue 组件源码和 TypeScript 接口
- 📝 **文档自动生成**：生成结构化的中文技术文档
- 🎨 **示例代码生成**：自动创建可运行的 Demo 示例
- 🔧 **多种配置选项**：支持指定组件、强制重新生成等
- 🌐 **多AI服务支持**：支持 OpenAI、百度文心、阿里通义等

## 安装依赖

```bash
# 安装必要的依赖
npm install axios dotenv
```

## 环境配置

### 1. 创建环境变量文件

在项目根目录创建 `.env.local` 文件：

```bash
# AI 配置
AI_API_KEY=your_openai_api_key_here
AI_MODEL=gpt-4o-mini
AI_BASE_URL=https://api.openai.com/v1
```

### 2. 不同AI服务配置

#### OpenAI
```bash
AI_API_KEY=sk-xxxxxxxxxxxxxxxx
AI_MODEL=gpt-4o-mini
AI_BASE_URL=https://api.openai.com/v1
```

#### 百度文心一言
```bash
AI_API_KEY=your_baidu_api_key
AI_MODEL=ernie-bot-turbo
AI_BASE_URL=https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop
```

#### 阿里通义千问
```bash
AI_API_KEY=your_alibaba_api_key
AI_MODEL=qwen-turbo
AI_BASE_URL=https://dashscope.aliyuncs.com/api/v1
```

## 使用方法

### 基础命令

```bash
# 生成所有组件文档
npm run docs:generate-ai

# 生成指定组件文档
npm run docs:ai-single button-copy

# 强制重新生成（覆盖已存在的文档）
npm run docs:ai-force

# 只生成 Demo，不生成文档
npm run docs:demo-only
```

### 高级选项

```bash
# 生成指定组件的文档
node scripts/ai-docs-generator.js --component=carousel

# 强制重新生成所有文档
node scripts/ai-docs-generator.js --force

# 只生成Demo示例
node scripts/ai-docs-generator.js --demo-only

# 组合使用多个选项
node scripts/ai-docs-generator.js --component=button-copy --force
```

## 生成的文件结构

运行后会生成以下文件结构：

```
docs/components/
├── button-copy.md            # 组件文档
├── carousel.md
└── ...

demo/
├── button-copy/
│   └── basic.vue             # 基础示例
├── carousel/
│   ├── basic.vue
│   └── advanced.vue          # 高级示例（复杂组件）
└── ...
```

## 文档内容

自动生成的文档包含：

1. **组件介绍**：功能描述和特性说明
2. **基础用法**：简单的使用示例
3. **API 文档**：Props、Events、Slots 详细说明
4. **使用场景**：典型应用场景描述
5. **注意事项**：使用时的注意点
6. **示例代码**：完整的使用示例

## 配置说明

### AI 配置文件

在 `scripts/ai-config.js` 中可以调整：

- **API 参数**：超时时间、重试次数等
- **生成参数**：温度值、最大tokens等
- **Prompt 模板**：自定义AI提示词
- **生成规则**：文件命名、输出路径等

### Prompt 优化

项目内置了多个优化的 Prompt 模板：

1. **组件分析模板**：深度分析组件功能和特性
2. **文档生成模板**：生成标准格式的技术文档
3. **Demo 生成模板**：创建可运行的示例代码
4. **API 文档模板**：生成详细的API说明

## 故障排除

### 常见问题

1. **API 密钥错误**
   ```
   错误：请在环境变量中设置 AI_API_KEY
   解决：检查 .env.local 文件中的 API 密钥配置
   ```

2. **网络连接问题**
   ```
   错误：AI调用失败，使用备用模板生成文档
   解决：检查网络连接和 API 服务状态
   ```

3. **组件不存在**
   ```
   错误：组件 xxx 不存在
   解决：确认组件名称正确，检查 helper-components 目录
   ```

### 调试模式

在脚本中添加调试信息：

```bash
DEBUG=ai-docs node scripts/ai-docs-generator.js
```

## 最佳实践

1. **分步生成**：对于复杂项目，建议先生成单个组件测试效果
2. **人工审核**：AI生成的文档建议人工检查和优化
3. **模板调优**：根据项目特点调整 Prompt 模板
4. **版本控制**：生成的文档提交到版本控制系统

## 扩展开发

### 添加新的AI服务

在 `ai-config.js` 中添加新的服务配置：

```javascript
export const AI_PROVIDERS = {
  CUSTOM: {
    name: '自定义AI',
    baseURL: 'https://your-api-endpoint.com',
    models: ['custom-model-1', 'custom-model-2']
  }
}
```

### 自定义Prompt模板

修改 `PROMPTS` 对象中的模板：

```javascript
CUSTOM_PROMPT: `
你的自定义提示词模板...
{变量占位符}
`
```

## 技术支持

如有问题或建议，请：

1. 查看项目文档和示例
2. 检查环境配置是否正确
3. 查看控制台错误信息
4. 联系开发团队

---

**注意**：请妥善保管 API 密钥，不要将其提交到版本控制系统中。 