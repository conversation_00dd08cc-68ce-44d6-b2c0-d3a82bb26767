import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * 生成组件文档
 * @param {string} componentName 组件名称
 */
async function generateComponentDocs(componentName) {
  console.log(`🔍 正在分析组件: ${componentName}`)
  
  try {
    // 1. 分析组件源码
    const componentInfo = await analyzeComponent(componentName)
    
    // 2. 生成文档目录
    const docsDir = path.join(__dirname, '../docs/components')
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true })
    }

    // 3. 生成文档内容
    const docContent = generateDocTemplate(componentInfo)
    const demoContent = generateDemoTemplate(componentInfo)

    // 4. 写入文件
    fs.writeFileSync(path.join(docsDir, `${componentName}.md`), docContent)
    
    // 5. 创建demo目录和文件
    const demoDir = path.join(__dirname, '../demo', componentName)
    if (!fs.existsSync(demoDir)) {
      fs.mkdirSync(demoDir, { recursive: true })
    }
    fs.writeFileSync(path.join(demoDir, 'basic.vue'), demoContent)
    
    // 6. 更新导航配置
    updateSidebarConfig(componentName, componentInfo)
    
    console.log(`✅ 已为 ${componentName} 生成文档`)
    
  } catch (error) {
    console.error(`❌ 生成 ${componentName} 文档失败:`, error.message)
    throw error
  }
}

/**
 * 分析组件源码，提取信息
 * @param {string} componentName 组件名称
 * @returns {Object} 组件信息
 */
async function analyzeComponent(componentName) {
  const componentPath = path.resolve(__dirname, '../helper-components', componentName)
  
  let componentInfo = {
    name: componentName,
    displayName: componentName,
    description: '',
    props: [],
    events: [],
    slots: [],
    hasAssets: false,
    hasApi: false
  }
  
  if (!fs.existsSync(componentPath)) {
    console.warn(`⚠️  组件目录不存在: ${componentPath}`)
    return componentInfo
  }
  
  // 检查是否有assets目录
  componentInfo.hasAssets = fs.existsSync(path.join(componentPath, 'assets'))
  
  // 检查是否有api文件
  componentInfo.hasApi = fs.existsSync(path.join(componentPath, 'api.ts'))
  
  // 分析Vue组件文件
  const vueFiles = ['index.vue', `${componentName}.vue`]
  for (const vueFile of vueFiles) {
    const vuePath = path.join(componentPath, vueFile)
    if (fs.existsSync(vuePath)) {
      const vueContent = fs.readFileSync(vuePath, 'utf-8')
      componentInfo = parseVueComponent(vueContent, componentInfo)
      break
    }
  }
  
  // 分析TypeScript接口文件
  const interfacePath = path.join(componentPath, 'interface.ts')
  if (fs.existsSync(interfacePath)) {
    const interfaceContent = fs.readFileSync(interfacePath, 'utf-8')
    componentInfo = parseTypeScriptInterface(interfaceContent, componentInfo)
  }
  
  // 从README.md中提取描述信息
  const readmePath = path.resolve(__dirname, '../helper-components/README.md')
  if (fs.existsSync(readmePath)) {
    const readmeContent = fs.readFileSync(readmePath, 'utf-8')
    componentInfo.description = extractDescriptionFromReadme(readmeContent, componentName)
  }
  
  return componentInfo
}

/**
 * 解析Vue组件文件
 * @param {string} content Vue文件内容
 * @param {Object} info 组件信息
 * @returns {Object} 更新后的组件信息
 */
function parseVueComponent(content, info) {
  // 提取Props (简单的正则匹配，可以扩展为AST解析)
  const propsRegex = /(?:interface\s+Props\s*{([^}]+)}|defineProps<[^{]*{([^}]+)})/gs
  let match
  
  while ((match = propsRegex.exec(content)) !== null) {
    const propsContent = match[1] || match[2]
    if (propsContent) {
      const props = parsePropsFromTypeScript(propsContent)
      info.props.push(...props)
    }
  }
  
  // 提取Emits
  const emitsRegex = /(?:interface\s+Emits\s*{([^}]+)}|defineEmits<[^{]*{([^}]+)})/gs
  while ((match = emitsRegex.exec(content)) !== null) {
    const emitsContent = match[1] || match[2]
    if (emitsContent) {
      const events = parseEventsFromTypeScript(emitsContent)
      info.events.push(...events)
    }
  }
  
  return info
}

/**
 * 解析TypeScript接口文件
 * @param {string} content TypeScript文件内容
 * @param {Object} info 组件信息
 * @returns {Object} 更新后的组件信息
 */
function parseTypeScriptInterface(content, info) {
  // 这里可以使用更复杂的AST解析，暂时使用简单正则
  const interfaceRegex = /interface\s+(\w+Props|\w+Config)\s*{([^}]+)}/gs
  let match
  
  while ((match = interfaceRegex.exec(content)) !== null) {
    const interfaceContent = match[2]
    if (interfaceContent) {
      const props = parsePropsFromTypeScript(interfaceContent)
      info.props.push(...props)
    }
  }
  
  return info
}

/**
 * 从TypeScript代码中解析Props
 * @param {string} propsContent Props代码内容
 * @returns {Array} Props数组
 */
function parsePropsFromTypeScript(propsContent) {
  const props = []
  const lines = propsContent.split('\n')
  
  for (let line of lines) {
    line = line.trim()
    if (!line || line.startsWith('//') || line.startsWith('*')) continue
    
    // 匹配 /** 注释 */ propName?: type
    const commentMatch = line.match(/\/\*\*\s*([^*]+?)\s*\*\//)
    const propMatch = line.match(/(\w+)(\?)?\s*:\s*([^;,\n]+)/)
    
    if (propMatch) {
      const [, name, optional, type] = propMatch
      props.push({
        name,
        type: type.trim(),
        required: !optional,
        description: commentMatch ? commentMatch[1].trim() : '',
        default: optional ? 'undefined' : '-'
      })
    }
  }
  
  return props
}

/**
 * 从TypeScript代码中解析Events
 * @param {string} eventsContent Events代码内容
 * @returns {Array} Events数组
 */
function parseEventsFromTypeScript(eventsContent) {
  const events = []
  const lines = eventsContent.split('\n')
  
  for (let line of lines) {
    line = line.trim()
    if (!line || line.startsWith('//') || line.startsWith('*')) continue
    
    const commentMatch = line.match(/\/\*\*\s*([^*]+?)\s*\*\//)
    const eventMatch = line.match(/(\w+)\s*:\s*\[([^\]]*)\]/)
    
    if (eventMatch) {
      const [, name, params] = eventMatch
      events.push({
        name,
        params: params.trim() || '-',
        description: commentMatch ? commentMatch[1].trim() : ''
      })
    }
  }
  
  return events
}

/**
 * 从README中提取组件描述
 * @param {string} readmeContent README内容
 * @param {string} componentName 组件名称
 * @returns {string} 组件描述
 */
function extractDescriptionFromReadme(readmeContent, componentName) {
  const patterns = [
    new RegExp(`### ${componentName}[\\s\\S]*?#### 功能\\s*([^#]+)`, 'i'),
    new RegExp(`## ${componentName}[\\s\\S]*?([^#]+)`, 'i')
  ]

  for (const pattern of patterns) {
    const match = readmeContent.match(pattern)
    if (match) {
      return match[1].trim().split('\n')[0].trim()
    }
  }

  return `${componentName}组件`
}

/**
 * 转换为Pascal命名法的组件名
 * @param {string} componentName 组件名称（kebab-case）
 * @returns {string} Pascal命名法的组件名
 */
function toPascalCase(componentName) {
  return componentName
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('')
}



/**
 * 生成文档模板
 * @param {Object} componentInfo 组件信息
 * @returns {string} 文档内容
 */
function generateDocTemplate(componentInfo) {
  const { name, displayName, description, props, events, slots, hasAssets, hasApi } = componentInfo
  const componentPascalName = toPascalCase(name)
  
  return `# ${displayName}

${description || `${displayName}是一个功能丰富的Vue组件。`}

## 基础用法

<DemoContainer>
<template #demo>
<${name.split('-').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join('')} />
</template>
<template #code>

\`\`\`vue
<template>
  <${componentPascalName} ${props.slice(0, 2).map(prop => `:${prop.name}="${prop.name}"`).join(' ')} />
</template>

<script setup>
import { ref } from 'vue'
import ${componentPascalName} from 'helper-components/${name}/index.vue'

${props.slice(0, 2).map(prop => 
  `const ${prop.name} = ref(${getDefaultExampleValue(prop)})`
).join('\n')}
</script>
\`\`\`

</template>
</DemoContainer>

## API

### Props

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
${props.map(prop => 
  `| ${prop.name} | ${prop.description || '暂无说明'} | \`${prop.type}\` | ${prop.default} | ${prop.required ? '是' : '否'} |`
).join('\n')}

${events.length > 0 ? `### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
${events.map(event => 
  `| ${event.name} | ${event.description || '暂无说明'} | ${event.params} |`
).join('\n')}` : ''}

${slots.length > 0 ? `### Slots

| 插槽名 | 说明 | 参数 |
|--------|------|------|
${slots.map(slot => 
  `| ${slot.name} | ${slot.description || '暂无说明'} | ${slot.params || '-'} |`
).join('\n')}` : ''}

## 使用示例

### 基础示例

\`\`\`vue
<template>
  <${componentPascalName} 
    ${props.filter(p => p.required).map(prop => `:${prop.name}="${prop.name}"`).join('\n    ')}
  />
</template>

<script setup>
import { ref } from 'vue'
import ${componentPascalName} from 'helper-components/${name}/index.vue'

${props.filter(p => p.required).map(prop => 
  `const ${prop.name} = ref(${getDefaultExampleValue(prop)})`
).join('\n')}
</script>
\`\`\`

${hasApi ? `### API 调用

如果组件支持API调用，可以这样使用：

\`\`\`javascript
import { ${name}Api } from 'helper-components/${name}/api'

// 调用API
const data = await ${name}Api.getData()
\`\`\`

` : ''}${props.length > 3 ? `### 高级配置

\`\`\`vue
<template>
  <${componentPascalName} 
    ${props.map(prop => `:${prop.name}="${prop.name}"`).join('\n    ')}
    ${events.map(event => `@${event.name}="handle${event.name.charAt(0).toUpperCase() + event.name.slice(1)}"`).join('\n    ')}
  />
</template>

<script setup>
import { ref } from 'vue'
import ${componentPascalName} from 'helper-components/${name}/index.vue'

${props.map(prop => 
  `const ${prop.name} = ref(${getDefaultExampleValue(prop)})`
).join('\n')}

${events.map(event => 
  `const handle${event.name.charAt(0).toUpperCase() + event.name.slice(1)} = (${event.params !== '-' ? event.params.split(':')[0] : 'data'}) => {
  console.log('${event.name}:', ${event.params !== '-' ? event.params.split(':')[0] : 'data'})
}`
).join('\n\n')}
</script>
\`\`\`

` : ''}## 注意事项

1. 确保已正确安装和引入组件
2. ${props.filter(p => p.required).length > 0 ? `必填参数：${props.filter(p => p.required).map(p => `\`${p.name}\``).join('、')}` : '该组件无必填参数'}
3. ${hasAssets ? '该组件包含样式资源，请确保样式正确加载' : '该组件不依赖额外的样式资源'}

${description.includes('WeGame') || hasApi ? `## 依赖说明

该组件可能需要以下依赖：
- \`@tencent/wegame-web-sdk\` - WeGame SDK支持
- \`vue\` - Vue 3框架
${hasApi ? '- 网络连接 - 用于API数据获取' : ''}

` : ''}---

> 如果在使用过程中遇到问题，请查看 [开发指南](/guide/development) 或提交 Issue。`
}

/**
 * 生成Demo模板
 * @param {Object} componentInfo 组件信息
 * @returns {string} Demo内容
 */
function generateDemoTemplate(componentInfo) {
  const { name, props, events } = componentInfo
  const componentPascalName = toPascalCase(name)
  
  return `<template>
  <div class="demo-wrapper">
    <div class="demo-preview">
      <${componentPascalName}
        ${props.slice(0, 5).map(prop => `:${prop.name}="${prop.name}"`).join('\n        ')}
        ${events.slice(0, 3).map(event => `@${event.name}="handle${event.name.charAt(0).toUpperCase() + event.name.slice(1)}"`).join('\n        ')}
      />
    </div>
    
    ${props.length > 0 ? `<div class="demo-controls">
      <h4>属性配置</h4>
      ${props.slice(0, 5).map(prop => `
      <div class="demo-control">
        <label>${prop.name}:</label>
        <input 
          v-model="${prop.name}" 
          type="${getInputType(prop.type)}"
          placeholder="${prop.description || ''}"
        />
      </div>`).join('')}
    </div>` : ''}
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 导入组件
import ${componentPascalName} from 'helper-components/${name}/index.vue'

// 响应式属性
${props.slice(0, 5).map(prop => 
  `const ${prop.name} = ref(${getDefaultExampleValue(prop)})`
).join('\n')}

// 事件处理函数
${events.slice(0, 3).map(event => 
  `const handle${event.name.charAt(0).toUpperCase() + event.name.slice(1)} = (${event.params !== '-' ? event.params.split(':')[0] || 'data' : 'data'}) => {
  console.log('${event.name}:', ${event.params !== '-' ? event.params.split(':')[0] || 'data' : 'data'})
}`
).join('\n\n')}
</script>

<style scoped>
.demo-wrapper {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.demo-preview {
  padding: 24px;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.demo-controls {
  padding: 16px;
  background-color: #f5f5f5;
  border-top: 1px solid #eee;
}

.demo-controls h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
}

.demo-control {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.demo-control:last-child {
  margin-bottom: 0;
}

.demo-control label {
  min-width: 80px;
  font-size: 12px;
  color: #333;
}

.demo-control input {
  flex: 1;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
}

.demo-control input[type="checkbox"] {
  flex: none;
  width: auto;
}
</style>`
}

/**
 * 获取输入框类型
 * @param {string} type 属性类型
 * @returns {string} 输入框类型
 */
function getInputType(type) {
  if (type.includes('number')) return 'number'
  if (type.includes('boolean')) return 'checkbox'
  if (type.includes('Date')) return 'date'
  return 'text'
}

/**
 * 获取默认示例值
 * @param {Object} prop 属性对象
 * @returns {string} 默认值
 */
function getDefaultExampleValue(prop) {
  const { type, name } = prop
  
  if (type.includes('string')) {
    if (name.includes('Id') || name.includes('id')) return "'example-id'"
    if (name.includes('url') || name.includes('Url')) return "'https://example.com'"
    if (name.includes('title') || name.includes('Title')) return "'示例标题'"
    return "'示例文本'"
  }
  
  if (type.includes('number')) {
    if (name.includes('count') || name.includes('Count')) return '5'
    if (name.includes('max') || name.includes('Max')) return '10'
    if (name.includes('min') || name.includes('Min')) return '1'
    return '0'
  }
  
  if (type.includes('boolean')) {
    if (name.includes('visible') || name.includes('show')) return 'true'
    return 'false'
  }
  
  if (type.includes('array') || type.includes('Array')) return '[]'
  if (type.includes('object') || type.includes('Object')) return '{}'
  
  return "''"
}

/**
 * 更新侧边栏配置
 * @param {string} componentName 组件名称
 * @param {Object} componentInfo 组件信息
 */
function updateSidebarConfig(componentName, componentInfo) {
  const configPath = path.join(__dirname, '../.vitepress/config.ts')
  
  if (!fs.existsSync(configPath)) {
    console.warn('配置文件不存在，跳过侧边栏更新')
    return
  }
  
  let config = fs.readFileSync(configPath, 'utf-8')
  const { category, displayName } = componentInfo
  
  // 查找对应分类的位置
  const categoryRegex = new RegExp(`text: '${category}'[\\s\\S]*?items: \\[([\\s\\S]*?)\\]`, 'g')
  const match = categoryRegex.exec(config)
  
  if (match) {
    const items = match[1]
    const newItem = `            { text: '${displayName}', link: '/components/${componentName}/' }`
    
    // 检查是否已存在
    if (!items.includes(`/components/${componentName}/`)) {
      const updatedItems = items.trim() ? `${items.trim()},\n${newItem}` : newItem
      config = config.replace(match[1], `\n${updatedItems}\n          `)
      
      fs.writeFileSync(configPath, config)
      console.log(`✅ 已更新侧边栏配置: ${displayName}`)
    }
  } else {
    console.warn(`⚠️  未找到 "${category}" 分类，无法自动更新侧边栏`)
  }
}

// 命令行调用
if (process.argv[1] === __filename) {
  const componentName = process.argv[2]
  
  if (!componentName) {
    console.error('❌ 请提供组件名称')
    console.log('用法: node generate-component-docs.js <component-name>')
    console.log('示例: node generate-component-docs.js carousel')
    process.exit(1)
  }
  
  generateComponentDocs(componentName)
    .then(() => {
      console.log(`🎉 ${componentName} 文档生成完成！`)
    })
    .catch(err => {
      console.error('💥 文档生成失败:', err)
      process.exit(1)
    })
}

export default generateComponentDocs 