import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import generateComponentDocs from './generate-component-docs.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * 更新组件文档
 * @param {string} componentName 组件名称
 */
async function updateComponentDocs(componentName) {
  console.log(`🔄 更新组件文档: ${componentName}`)
  
  try {
    const docsPath = path.join(__dirname, '../components', componentName, 'index.md')
    
    if (fs.existsSync(docsPath)) {
      console.log(`📄 发现现有文档，进行更新...`)
      
      // 备份现有文档
      const backupPath = `${docsPath}.backup`
      fs.copyFileSync(docsPath, backupPath)
      
      try {
        // 重新生成文档
        await generateComponentDocs(componentName)
        
        // 删除备份
        fs.unlinkSync(backupPath)
        
        console.log(`✅ ${componentName} 文档更新完成`)
        
      } catch (error) {
        // 恢复备份
        if (fs.existsSync(backupPath)) {
          fs.copyFileSync(backupPath, docsPath)
          fs.unlinkSync(backupPath)
          console.log(`💾 已恢复原文档`)
        }
        throw error
      }
      
    } else {
      console.log(`📝 文档不存在，创建新文档...`)
      await generateComponentDocs(componentName)
    }
    
  } catch (error) {
    console.error(`❌ 更新 ${componentName} 文档失败:`, error.message)
    throw error
  }
}

// 命令行调用
if (process.argv[1] === __filename) {
  const componentName = process.argv[2]
  
  if (!componentName) {
    console.error('❌ 请提供组件名称')
    console.log('用法: node update-component-docs.js <component-name>')
    console.log('示例: node update-component-docs.js carousel')
    process.exit(1)
  }
  
  updateComponentDocs(componentName)
    .then(() => {
      console.log(`🎉 ${componentName} 文档更新完成！`)
    })
    .catch(err => {
      console.error('💥 文档更新失败:', err)
      process.exit(1)
    })
}

export default updateComponentDocs 