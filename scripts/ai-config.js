import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 加载环境变量
dotenv.config({ path: path.resolve(__dirname, '../.env.local') })
dotenv.config() // 加载默认的 .env 文件

/**
 * AI 配置
 */
export const AI_CONFIG = {
  // API 配置
  api: {
    key: process.env.AI_API_KEY || '',
    model: process.env.AI_MODEL || 'llama3.2:1b',
    baseURL: process.env.AI_BASE_URL || 'http://127.0.0.1:11434/v1',
    timeout: 6000000,
    maxRetries: 3
  },
  
  // 生成配置
  generation: {
    temperature: 0.7,
    maxTokens: 60000,
    topP: 0.9
  }
}

/**
 * Prompt 模板
 */
export const PROMPTS = {
  // 组件分析 Prompt
  ANALYZE_COMPONENT: `
你是一个专业的前端组件文档专家。请基于以下Vue组件的源码，分析组件的功能、特性和使用场景。

## 组件源码：
{componentCode}

## 组件接口定义：
{interfaceCode}

## 分析要求：
1. 总结组件的核心功能（1-2句话）
2. 列出主要特性（3-5个要点）
3. 描述典型使用场景（2-3个）
4. 识别组件类别（UI组件/业务组件/工具组件）

请以JSON格式返回分析结果：
{
  "description": "组件核心功能描述",
  "features": ["特性1", "特性2", "特性3"],
  "useCases": ["使用场景1", "使用场景2"],
  "category": "组件类别"
}
`,

  // 文档生成 Prompt
  GENERATE_DOCS: `
你是一个专业的前端组件库的组件文档撰写专家。请基于以下组件信息，生成完整的中文组件使用指导文档。

## 组件信息：
- 名称：{componentName}
- 组件源码：{sourceCode}

## 标准文档模板参考：
{templateReference}

## 文档要求：
1. 使用中文撰写，技术术语保留英文
2. 结构清晰，包含完整的API说明
3. 示例代码要完整，简单明了
4. 符合VitePress Markdown格式
5. 提供了helper-button组件的文档模板，供参考

## 文档需要包含的板块内容
1. 组件名称
   注：组件英文名即{componentName}
2. 介绍
   注：组件功能介绍
3. 页面模版
   注：页面模版固定展示：<demo src="{componentName}/example.vue"/>
4. 组件功能板块
   注：a.第一个板块为基础用法，其他板块内容根据组件特性生成
      b.每个板块需要包含板块介绍与示例代码
      c. 每个板块的示例代码要求:
        1. 使用<script setup>语法糖的书写方式
        2. 导入语句必须保持固定格式:helper-components/{componentName}/index.vue
        3. 导入后的组件名称使用PascalCase命名方式，如：<ButtonCopy />
        4. 示例代码要完整，为可直接在vue文件中渲染的代码
        5. 尽可能少的添加样式，保持代码简洁
5. API说明
   注：API说明板块根据组件特性，按需包含Props、Events、Slots三个板块,以表格方式呈现
6. 使用建议与注意事项
   注：使用建议与注意事项板块需要包含使用建议与注意事项说明

## 输出要求：
1. 直接输出完整的组件使用介绍markdown文档内容，不要任何前缀说明或解释
2. 确保内容完整，包含所有章节
3. 不要使用代码块包装整个文档

`,

  // Demo 生成 Prompt
  GENERATE_DEMO: `
你是一个专业的Vue开发者。将提供你前端vue组件，请基于以下组件信息，生成可运行的组件使用的Vue示例dmeo代码，用于展示组件的功能。

## 组件信息：
- 名称：{componentName}
- 路径：{componentPath}
- Props：{props}
- 分析结果：{analysis}
- 组件源码：{sourceCode}

## 示例要求：
1. 生成组件使用示例的，这个示例用于直接写入vue文件中
3. 代码需要可直接运行
4. 展示主要功能和典型用法
5. 使用合理的测试数据
6. 代码简洁明了，重点突出组件特性

## 重要说明：
- 必须严格按照下面的模板格式
- 导入语句必须保持固定格式，不能修改
- 组件名称在模板中使用PascalCase（如ButtonCopy）
- 导入路径固定为：helper-components/组件名/index.vue

## 基础示例模板（必须严格遵循）：
<template>
  <div>
    <!-- 在这里使用组件，组件名称使用PascalCase -->
  </div>
</template>

<script setup lang="ts">
import ButtonCopy from 'helper-components/{componentPath}/index.vue'
// 其他必要导入
// 响应式数据和方法
</script>

<style scoped>
/* 其他必要样式 */
</style>

## 输出要求：
1. 直接输出vue代码，不要任何前缀说明或解释
2. 严格按照上面的模板格式
3. 导入语句中的ButtonCopy要替换为对应组件的PascalCase名称
4. 模板中使用组件时也要使用PascalCase名称
5. 不要使用代码块包装整个文件
`,
}

/**
 * 生成规则配置
 */
export const GENERATION_RULES = {
  // 文档生成选项
  DOC_OPTIONS: {
    includeDemo: true,
    includeAPI: true,
    generateAdvancedDemo: false, // 根据组件复杂度决定
    autoUpdateSidebar: true
  },
  
  // 输出目录配置
  PATHS: {
    docs: 'docs/components',
    demos: 'demo',
    assets: 'public/assets'
  }
}