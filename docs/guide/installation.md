# 安装使用

本页面将详细介绍如何在你的项目中安装和使用 Helper Components。

## 使用 Git Subtree

Git Subtree 是推荐的安装方式，它可以将组件库作为子项目集成到你的主项目中。

### 初次添加

```bash
# 添加组件库作为子项目
git subtree add --prefix=shared/helper-components ***************:wegame/helper/helper-components.git master --squash
```

参数说明：
- `--prefix=shared/helper-components`: 指定组件库在项目中的存放路径
- `***************:wegame/helper/helper-components.git`: 组件库的 Git 地址
- `master`: 使用的分支名
- `--squash`: 压缩提交历史，保持项目历史整洁

### 更新组件库

当组件库有更新时，使用以下命令同步最新代码：

```bash
git subtree pull --prefix=shared/helper-components ***************:wegame/helper/helper-components.git master --squash
```

### 推送更改

如果你对组件库进行了修改并希望贡献回主仓库：

```bash
git subtree push --prefix=shared/helper-components ***************:wegame/helper/helper-components.git master
```

::: warning 注意
推送前请确保你的修改已经过充分测试，并且符合组件库的开发规范。
:::

### 常见问题解决

#### 合并冲突

```bash
fatal: You have not concluded your merge (MERGE_HEAD exists).
```

解决方法：
```bash
git reset --hard
```

#### 工作区有修改

```bash
fatal: working tree has modifications. Cannot add.
```

解决方法：
```bash
git status
# 查看并处理工作区的修改
git add .
git commit -m "save changes before subtree operation"
```

## 项目配置

### Vue 项目配置

如果你使用的是 Vue CLI 或 Vite 项目，建议配置路径别名：

#### Vite 配置

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'shared/helper-components')
    }
  }
})
```

#### Vue CLI 配置

```javascript
// vue.config.js
const { defineConfig } = require('@vue/cli-service')
const path = require('path')

module.exports = defineConfig({
  configureWebpack: {
    resolve: {
      alias: {
        '@components': path.resolve(__dirname, 'shared/helper-components')
      }
    }
  }
})
```

### TypeScript 配置

如果你的项目使用 TypeScript，添加路径映射：

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@components/*": ["shared/helper-components/*"]
    }
  }
}
```

## 依赖管理

### 核心依赖

组件库需要以下核心依赖，请确保你的项目已安装：

```json
{
  "dependencies": {
    "vue": "^3.3.0",
    "@vueuse/core": "^10.0.0",
    "dayjs": "^1.11.0"
  }
}
```

### WeGame SDK

如果使用 WeGame 相关功能，需要安装 WeGame SDK：

```bash
npm install @tencent/wegame-web-sdk
```

### 视频播放器

如果使用视频相关组件，需要安装超级播放器：

```bash
npm install @tencent/super-player
```

## 使用示例

### 基础组件使用

```vue
<template>
  <div class="app">
    <!-- 适龄提示 -->
    <AgeTips :gameId="gameId" />
    
    <!-- 轮播图 -->
    <Carousel 
      :appId="appId"
      :navBtnVisible="true"
      :loop="true"
    />
    
    <!-- 视频列表 -->
    <VideoList 
      :appId="appId"
      :rows="2"
      :cols="3"
    />
  </div>
</template>

<script setup>
import AgeTips from '@components/age-tips'
import Carousel from '@components/carousel'
import VideoList from '@components/video-list'

const gameId = '26'
const appId = 'your-app-id'
</script>
```

### 工具函数使用

```javascript
// 页面跳转
import { jumpPage } from '@components/utils/jump'

// WeGame 浏览器中打开
jumpPage({
  url: 'https://www.wegame.com.cn/store/1/',
  jump_url_type: 'new_wegame_window'
})

// 系统浏览器中打开
jumpPage({
  url: 'https://www.wegame.com.cn'
})
```

### Composables 使用

```vue
<script setup>
import { useAudio } from '@components/composable/use-audio'
import { useGameState } from '@components/composable/use-game-state'

// 全局音频控制
const audioStore = useAudio()

// 游戏状态管理
const { isRunning, gameInfo } = useGameState('your-game-id')

// 播放音频
const playBackgroundMusic = () => {
  audioStore.play()
}
</script>
```

## 按需引入

组件库支持按需引入，你只需要引入实际使用的组件：

```vue
<script setup>
// 只引入需要的组件
import AgeTips from '@components/age-tips'
import Carousel from '@components/carousel'

// 只引入需要的工具函数
import { jumpPage } from '@components/utils/jump'
import { uuid } from '@components/utils/uuid'
</script>
```

这种方式可以减少打包体积，提高应用性能。

## 下一步

- [查看所有组件](/components/) - 浏览可用的组件
- [开发指南](/guide/development) - 了解如何参与开发 