# 快速开始

Helper Components 是为 WeGame 项目开发的 Vue 3 组件库，提供了丰富的业务组件和工具函数。

## 安装

### 使用 Git Subtree（推荐）

在你的项目中使用 git subtree 命令添加组件库：

```bash
# 添加子仓库
git subtree add --prefix=shared/helper-components ***************:wegame/helper/helper-components.git master --squash

# 更新子仓库
git subtree pull --prefix=shared/helper-components ***************:wegame/helper/helper-components.git master --squash
```

### 直接下载

你也可以直接从 GitLab 下载最新版本的代码。

## 基础使用

### 引入组件

```vue
<template>
  <div>
    <!-- 使用适龄提示组件 -->
    <AgeTips :gameId="gameId" />
    
    <!-- 使用轮播图组件 -->
    <Carousel :appId="appId" />
  </div>
</template>

<script setup>
import AgeTips from 'shared/helper-components/age-tips'
import Carousel from 'shared/helper-components/carousel'

const gameId = '26'
const appId = 'your-app-id'
</script>
```

### 使用工具函数

```javascript
import { jumpPage } from 'shared/helper-components/utils/jump'
import { initMarketingFloater } from 'shared/helper-components/utils/marketing-floater'

// 页面跳转
jumpPage({
  url: 'https://www.wegame.com.cn',
  jump_url_type: 'new_wegame_window'
})

// 初始化营销浮动广告
initMarketingFloater('your-game-id')
```

### 使用 Composables

```vue
<script setup>
import { useAudio } from 'shared/helper-components/composable/use-audio'
import { useGameState } from 'shared/helper-components/composable/use-game-state'

// 音频控制
const { play, pause, volume } = useAudio()

// 游戏状态
const { isRunning, gameInfo } = useGameState('your-game-id')
</script>
```

## 项目结构

```
shared/helper-components/
├── age-tips/                 # 适龄提示组件
├── audio-player/            # 音频播放器
├── carousel/                # 轮播图组件
├── composable/              # 组合式 API
├── utils/                   # 工具函数
├── ui/                      # 基础 UI 组件
└── README.md
```

## 依赖说明

组件库依赖以下核心库：

- **Vue 3**: 前端框架
- **@vueuse/core**: Vue 工具集
- **dayjs**: 日期处理
- **@tencent/wegame-web-sdk**: WeGame Web SDK

部分组件还需要额外依赖：

- **@tencent/super-player**: 视频播放器相关组件

## 下一步

- [查看所有组件](/components/) - 浏览完整的组件列表
- [安装指南](/guide/installation) - 详细的安装和配置说明
- [开发指南](/guide/development) - 如何参与组件库开发

## 支持

如果你在使用过程中遇到问题，可以：

1. 查看本文档的相关页面
2. 在 GitLab 上提交 Issue
3. 联系 WeGame 前端团队 