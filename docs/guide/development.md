# 开发指南

本指南将帮助你了解如何参与 Helper Components 的开发和贡献。

## 开发环境搭建

### 环境要求

- Node.js >= 16
- Git
- Vue 3 基础知识
- TypeScript 基础知识（可选）

### 克隆项目

```bash
<NAME_EMAIL>:wegame/helper/helper-components.git
cd helper-components
```

### 本地开发

由于组件库是作为 subtree 使用的，本地开发时可以创建一个测试项目：

```bash
# 创建测试项目
mkdir helper-components-dev
cd helper-components-dev
npm init vue@latest test-app
cd test-app

# 添加组件库
git subtree add --prefix=shared/helper-components ../helper-components master --squash
```

## 项目结构

```
helper-components/
├── age-tips/                 # 适龄提示组件
│   ├── assets/              # 组件资源文件
│   ├── index.vue           # 组件主文件
│   └── index.ts            # 组件导出文件
├── audio-player/            # 音频播放器
├── carousel/                # 轮播图组件
├── composable/              # 组合式 API
│   ├── use-audio.ts
│   ├── use-game-state.ts
│   └── ...
├── utils/                   # 工具函数
│   ├── jump.ts
│   ├── storage.ts
│   └── ...
├── ui/                      # 基础 UI 组件
└── README.md
```

## 组件开发规范

### 组件目录结构

每个组件应该按照以下结构组织：

```
component-name/
├── assets/                  # 资源文件
│   ├── images/             # 图片资源
│   └── scss/               # 样式文件
├── components/             # 子组件（如果有）
├── api.ts                 # API 相关（如果有）
├── interface.ts           # 类型定义（如果有）
├── index.vue             # 主组件文件
└── index.ts              # 导出文件
```

### 组件命名规范

- 组件目录使用 kebab-case：`audio-player`、`video-list`
- 组件文件名使用 PascalCase：`AudioPlayer.vue`、`VideoList.vue`
- Props 使用 camelCase：`gameId`、`navBtnVisible`

### 组件开发模板

```vue
<template>
  <div class="helper-component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Props 定义
interface Props {
  /** 应用ID */
  appId?: string
  /** 是否显示导航按钮 */
  navBtnVisible?: boolean
  /** 其他属性... */
}

const props = withDefaults(defineProps<Props>(), {
  navBtnVisible: true
})

// Emits 定义
interface Emits {
  click: [data: any]
  change: [value: string]
}

const emit = defineEmits<Emits>()

// 响应式数据
const isLoading = ref(false)
const data = ref([])

// 计算属性
const displayData = computed(() => {
  return data.value.filter(item => item.visible)
})

// 生命周期
onMounted(() => {
  // 初始化逻辑
})

// 方法
const handleClick = (item: any) => {
  emit('click', item)
}
</script>

<style scoped lang="scss">
.helper-component-name {
  // 组件样式
  
  &__item {
    // BEM 命名规范
  }
  
  &--active {
    // 状态修饰符
  }
}
</style>
```

### Props 设计原则

1. **明确的类型定义**：使用 TypeScript 接口定义 Props
2. **合理的默认值**：为 Props 提供合理的默认值
3. **详细的注释**：为每个 Props 添加 JSDoc 注释
4. **向下兼容**：新增 Props 不应破坏现有 API

```typescript
interface Props {
  /** 应用程序的唯一标识符 */
  appId: string
  /** 是否显示左右翻页箭头 */
  navBtnVisible?: boolean
  /** 是否支持循环播放 */
  loop?: boolean
  /** 翻页指示器类型 1-圆点 2-数字 3-缩略图 */
  paginationType?: 1 | 2 | 3
  /** 最多显示的内容项数量 */
  maxSlides?: number
}
```

## 工具函数开发

### 工具函数结构

```typescript
// utils/example.ts

/**
 * 工具函数描述
 * @param param1 参数1描述
 * @param param2 参数2描述
 * @returns 返回值描述
 * @example
 * ```typescript
 * import { exampleFunction } from 'shared/helper-components/utils/example'
 * 
 * const result = exampleFunction('value1', 'value2')
 * ```
 */
export function exampleFunction(param1: string, param2?: string): string {
  // 函数实现
  return param1 + (param2 || '')
}

/**
 * 异步工具函数示例
 */
export async function asyncExampleFunction(url: string): Promise<any> {
  try {
    const response = await fetch(url)
    return await response.json()
  } catch (error) {
    console.error('API 请求失败:', error)
    throw error
  }
}
```

## Composables 开发

### Composables 规范

```typescript
// composable/use-example.ts
import { ref, computed, onMounted } from 'vue'

/**
 * 示例 Composable
 * @param initialValue 初始值
 * @returns 返回状态和方法
 */
export function useExample(initialValue?: string) {
  // 响应式状态
  const value = ref(initialValue || '')
  const isLoading = ref(false)
  
  // 计算属性
  const displayValue = computed(() => {
    return value.value.toUpperCase()
  })
  
  // 方法
  const setValue = (newValue: string) => {
    value.value = newValue
  }
  
  const fetchData = async () => {
    isLoading.value = true
    try {
      // 异步操作
      const data = await fetch('/api/data')
      value.value = await data.text()
    } finally {
      isLoading.value = false
    }
  }
  
  // 生命周期
  onMounted(() => {
    // 初始化逻辑
  })
  
  return {
    // 状态
    value: readonly(value),
    isLoading: readonly(isLoading),
    
    // 计算属性
    displayValue,
    
    // 方法
    setValue,
    fetchData
  }
}
```

## 测试规范

### 组件测试

```typescript
// tests/components/example.test.ts
import { mount } from '@vue/test-utils'
import ExampleComponent from '@/components/example/index.vue'

describe('ExampleComponent', () => {
  it('应该正确渲染', () => {
    const wrapper = mount(ExampleComponent, {
      props: {
        appId: 'test-app-id'
      }
    })
    
    expect(wrapper.exists()).toBe(true)
  })
  
  it('应该响应 props 变化', async () => {
    const wrapper = mount(ExampleComponent, {
      props: {
        appId: 'test-app-id',
        visible: false
      }
    })
    
    expect(wrapper.isVisible()).toBe(false)
    
    await wrapper.setProps({ visible: true })
    expect(wrapper.isVisible()).toBe(true)
  })
})
```

## 提交规范

### Commit Message 格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

#### Type 类型

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式修改
- `refactor`: 代码重构
- `test`: 测试用例修改
- `chore`: 构建工具或辅助工具修改

#### 示例

```
feat(carousel): 添加自动播放功能

- 新增 autoPlay 属性
- 添加播放间隔配置
- 支持鼠标悬停暂停

Closes #123
```

## 文档贡献

### 组件文档格式

每个组件都应该有对应的文档，包含：

1. **组件描述**：组件的用途和特性
2. **基础用法**：最简单的使用示例
3. **API 文档**：Props、Events、Slots 说明
4. **高级用法**：复杂场景的使用示例
5. **注意事项**：使用时需要注意的问题

### 文档自动生成

组件库支持自动生成基础文档，运行以下命令：

```bash
# 生成所有组件文档
npm run generate-docs

# 生成特定组件文档
npm run generate-docs carousel
```

## 发布流程

1. **功能开发**：在功能分支上开发新功能
2. **代码审查**：提交 Merge Request 进行代码审查
3. **测试验证**：确保所有测试通过
4. **合并主分支**：将功能分支合并到 master
5. **自动更新文档**：系统自动更新组件文档
6. **版本发布**：根据变更程度决定版本号

## 最佳实践

### 性能优化

1. **按需加载**：避免全量引入不必要的依赖
2. **代码分割**：大组件支持懒加载
3. **缓存优化**：合理使用缓存避免重复请求

### 兼容性

1. **向下兼容**：新版本不应破坏现有 API
2. **浏览器兼容**：支持主流浏览器的最新版本
3. **Vue 版本**：确保与 Vue 3 的兼容性

### 可维护性

1. **代码注释**：为复杂逻辑添加详细注释
2. **类型安全**：使用 TypeScript 确保类型安全
3. **单一职责**：每个组件和函数保持单一职责 