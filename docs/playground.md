# 代码编辑器测试

这是一个测试页面，用于验证代码编辑器的功能。

## 基础代码编辑器

<CodeEditor />

## 自定义初始代码的编辑器

<CodeEditor :initial-code="`<template>
  <div class='custom-demo'>
    <h2>{{ title }}</h2>
    <p>{{ description }}</p>
    <div class='counter'>
      <button @click='decrement'>-</button>
      <span class='count'>{{ count }}</span>
      <button @click='increment'>+</button>
    </div>
    <div class='actions'>
      <button @click='reset' class='reset-btn'>重置</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const title = ref('计数器示例')
const description = ref('这是一个可编辑的Vue计数器组件')
const count = ref(0)

const increment = () => {
  count.value++
}

const decrement = () => {
  count.value--
}

const reset = () => {
  count.value = 0
}
<\/script>

<style scoped>
.custom-demo {
  padding: 24px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  text-align: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

h2 {
  color: #2c3e50;
  margin-bottom: 8px;
}

p {
  color: #7f8c8d;
  margin-bottom: 20px;
}

.counter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 20px;
}

.counter button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #3498db;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.counter button:hover {
  background: #2980b9;
  transform: scale(1.1);
}

.count {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  min-width: 60px;
}

.actions {
  margin-top: 16px;
}

.reset-btn {
  padding: 8px 16px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.reset-btn:hover {
  background: #c0392b;
}
</style>`" />

## 使用说明

1. **编辑代码**: 在左侧编辑器中修改Vue单文件组件代码
2. **实时预览**: 右侧会实时显示组件的渲染效果
3. **格式化代码**: 点击格式化按钮美化代码
4. **重置代码**: 点击重置按钮恢复初始代码
5. **复制代码**: 点击复制按钮将代码复制到剪贴板
6. **新窗口打开**: 点击新窗口按钮在独立窗口中编辑

## 功能特性

- ✅ 语法高亮
- ✅ 代码自动补全
- ✅ 实时预览
- ✅ 错误提示
- ✅ 代码格式化
- ✅ 响应式设计
- ✅ 暗色主题支持

## 技术实现

本代码编辑器基于以下技术实现：

- **Monaco Editor**: 提供强大的代码编辑功能
- **Vue 3**: 组件框架和响应式系统
- **VitePress**: 文档站点生成器
- **自定义编译**: 简单的Vue SFC解析和预览

## 下一步计划

- [ ] 支持多文件编辑
- [ ] 集成TypeScript支持
- [ ] 添加更多代码模板
- [ ] 支持组件库导入
- [ ] 添加代码分享功能
