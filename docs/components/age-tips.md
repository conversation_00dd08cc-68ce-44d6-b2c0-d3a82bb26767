# age-tips 适龄提醒

::: tip 组件介绍
`age-tips` 组件用于展示游戏的适龄提醒信息。它会根据传入的 `gameId` 自动获取并展示相应的适龄图标。点击图标后，会弹出一个模态框，显示详细的适龄说明文案。
:::

## 页面模版

<demo src="age-tips/example.vue"/>

## 基础用法

`age-tips` 组件的使用非常简单。你只需要在页面中引入组件，并传入一个有效的 `gameId` 即可。组件会自动处理数据获取和交互逻辑。

当 `gameId` 有效且成功获取到数据时，组件会显示一个适龄图标。点击该图标会弹出一个包含详细信息的模态框。

```vue
<template>
  <div>
    <!-- 请将 'xxxxxx' 替换为实际的游戏ID -->
    <AgeTips game-id="xxxxxx" />
  </div>
</template>

<script lang="ts" setup>
// 导入 age-tips 组件
import AgeTips from 'helper-components/age-tips/index.vue';
</script>

<style scoped>
/* 容器样式，用于演示 */
div {
  position: relative;
  height: 100px;
}
</style>
```

## API

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| gameId | 游戏ID，为必填项。组件将根据此ID请求后端接口，获取对应的适龄提醒信息。 | `string` | — | `—` |

## 使用建议与注意事项

::: warning 注意事项
1.  **依赖外部数据**：该组件强依赖后端接口 (`/api/rail/web/data_filter/game_config/condition_search`) 来获取适龄提醒的图片和文案。请确保在部署环境中，前端页面可以正常访问此接口。
2.  **`gameId` 必填**：`gameId` 是一个必需的 `prop`。如果未提供或提供无效的 `gameId`，组件将无法获取数据，也不会被渲染到页面上。
3.  **内容渲染**：弹窗中的详细说明文案使用了 `v-html` 指令进行渲染。这要求接口返回的数据内容是安全可信的，以避免 XSS 攻击。
:::

::: tip 最佳实践
- **放置位置**：建议将此组件放置在页面统一的、易于查找的位置，例如页面底部（Footer）或侧边栏，作为游戏基本信息的补充。
- **数据准确性**：确保传递给组件的 `gameId` 与当前页面展示的游戏内容保持一致，以向用户提供准确的合规信息。
:::