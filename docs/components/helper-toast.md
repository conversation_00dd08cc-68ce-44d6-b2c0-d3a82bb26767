# helper-toast 轻提示

::: tip 组件介绍
用于在页面上显示短暂的、非侵入式的消息提示，常用于反馈操作结果。
:::

## 页面模版

<demo src="helper-toast/example.vue"/>

## 基础用法

最基础的用法，通过 `v-model` 控制显示与隐藏。

```vue
<template>
  <div>
    <button @click="showToast = true">显示提示</button>
    <HelperToast v-model="showToast" text="这是一条提示信息" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import HelperToast from 'helper-components/helper-toast/index.vue';

const showToast = ref(false);
</script>
```

## 不同位置

通过 `position` 属性可以设置提示出现的位置，可选值为 `center`、`bottom`。默认为 `center`。

```vue
<template>
  <div>
    <button @click="show('center')">居中显示</button>
    <button @click="show('bottom')" style="margin-left: 10px;">底部显示</button>
    <HelperToast v-model="visible" text="提示信息" :position="toastPosition" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import HelperToast from 'helper-components/helper-toast/index.vue';

const visible = ref(false);
const toastPosition = ref('center');

const show = (position) => {
  toastPosition.value = position;
  visible.value = true;
};
</script>
```

## 不同状态

通过 `icon` 属性可以为提示设置不同状态的图标，可选值为 `success`、`error`、`info`。

```vue
<template>
  <div>
    <button @click="show('success')">成功</button>
    <button @click="show('error')" style="margin-left: 10px;">失败</button>
    <button @click="show('info')" style="margin-left: 10px;">信息</button>

    <HelperToast
      v-model="visible"
      :text="toastText"
      :icon="toastIcon"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import HelperToast from 'helper-components/helper-toast/index.vue';

const visible = ref(false);
const toastText = ref('');
const toastIcon = ref('');

const show = (type) => {
  switch (type) {
    case 'success':
      toastText.value = '操作成功';
      toastIcon.value = 'success';
      break;
    case 'error':
      toastText.value = '发生错误';
      toastIcon.value = 'error';
      break;
    case 'info':
      toastText.value = '普通信息';
      toastIcon.value = 'info';
      break;
  }
  visible.value = true;
};
</script>
```

## 自定义时长

通过 `duration` 属性可以自定义提示的显示时长，单位为毫秒。默认值为 `3000`。

```vue
<template>
  <div>
    <button @click="showToast = true">显示5秒</button>
    <HelperToast v-model="showToast" text="我将在5秒后消失" :duration="5000" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import HelperToast from 'helper-components/helper-toast/index.vue';

const showToast = ref(false);
</script>
```

## 防止背景点击

设置 `preventClick` 属性为 `true`，可以在提示显示时添加一个透明遮罩，防止用户点击背景内容。

```vue
<template>
  <div>
    <button @click="showToast = true">显示提示并阻止点击</button>
    <HelperToast v-model="showToast" text="背景无法点击" prevent-click />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import HelperToast from 'helper-components/helper-toast/index.vue';

const showToast = ref(false);
</script>
```

## 自定义内容

组件提供了默认插槽 `default` 和具名插槽 `custom` 来自定义内容。`custom` 插槽的优先级高于 `default` 插槽和 `text`、`icon` 属性。

```vue
<template>
  <div>
    <button @click="showDefaultSlot = true">使用默认插槽</button>
    <button @click="showCustomSlot = true" style="margin-left: 10px;">使用具名插槽</button>

    <!-- 默认插槽示例 -->
    <HelperToast v-model="showDefaultSlot">
      <i>自定义内容</i>
    </HelperToast>

    <!-- 具名插槽示例 -->
    <HelperToast v-model="showCustomSlot">
      <template #custom>
        <div style="display: flex; align-items: center; flex-direction: column;">
          <img src="https://vuejs.org/logo.svg" alt="Vue Logo" width="30" height="30" />
          <p style="margin-top: 8px;">完全自定义的Toast</p>
        </div>
      </template>
    </HelperToast>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import HelperToast from 'helper-components/helper-toast/index.vue';

const showDefaultSlot = ref(false);
const showCustomSlot = ref(false);
</script>
```

## API

### Toast Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| modelValue | 是否显示，支持 `v-model` | boolean | `true` / `false` | `false` |
| text | 显示的文本内容 | string | — | — |
| position | 提示框位置 | string | `center` / `bottom` / `auto` | `center` |
| icon | 图标类型 | string | `info` / `success` / `error` | — |
| duration | 显示时长，单位为毫秒 | number | — | `3000` |
| preventClick | 是否显示遮罩层，防止背景点击 | boolean | `true` / `false` | `false` |

### Toast Events

| 事件名称 | 说明 | 回调参数 |
|----------|------|----------|
| update:modelValue | 在提示关闭时触发 | `(value: false)` |

### Toast Slots

| 插槽名称 | 说明 |
|----------|------|
| default | 自定义提示的文本内容。 |
| custom | 完全自定义提示内容，会覆盖 `icon` 和 `text` 属性以及 `default` 插槽。 |

## 使用建议与注意事项

::: warning 注意事项
1. `helper-toast` 是一个受控组件，必须使用 `v-model` 来控制其显示和隐藏。
2. 组件默认在 3 秒后自动关闭，并通过 `update:modelValue` 事件通知父组件。
3. 当使用 `custom` 插槽时，`icon`、`text` 属性和 `default` 插槽将不会生效。
4. 轻提示是轻量级的反馈，不应承载过多的交互和复杂内容。
:::

::: tip 最佳实践
- **文案简洁**: 提示文案应简明扼要，突出核心信息，避免过长的文本。
- **避免重要操作**: 轻提示不应用作需要用户确认的关键操作，因为它会自动消失且不中断用户流程。对于重要信息，请使用 `Dialog` 对话框等组件。
- **合理使用图标**: 使用 `icon` 属性可以帮助用户快速识别信息类型（成功、失败、提示），增强用户体验。
- **适时反馈**: 在用户完成一个操作后（如提交表单、复制成功），应立即给出反馈，增强操作的确定性。
:::