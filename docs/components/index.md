# 组件总览

Helper Components 是一个基于 Vue 3 的组件库，提供了丰富的 UI 组件和业务组件，帮助开发者快速构建高质量的 Web 应用。

## 🎯 设计原则

- **简洁易用**：API 设计简洁，易于理解和使用
- **高度可定制**：支持主题定制和样式覆盖
- **性能优先**：组件经过性能优化，确保流畅的用户体验
- **类型安全**：完整的 TypeScript 支持

## 📦 组件分类

### 基础组件

基础 UI 组件，提供常用的界面元素。

- [单选框 HelperRadio](/docs/components/helper-radio/) - 简洁美观的单选框组件
- [选择器 HelperSelect](/docs/components/helper-select/) - 功能丰富的下拉选择器
- [适龄提示 AgeTips](/docs/components/age-tips/) - 游戏适龄提示组件
- [复制按钮 ButtonCopy](/docs/components/button-copy/) - 一键复制功能按钮
- [加载动画 Loading](/docs/components/loading/) - 多种样式的加载动画
- [图片组件 HelperImage](/docs/components/helper-image/) - 增强的图片显示组件
- [信息组件 HelperInfo](/docs/components/helper-info/) - 信息展示组件

### 业务组件

针对特定业务场景设计的组件。

- [轮播图 Carousel](/docs/components/carousel/) - 图片轮播展示组件
- [音频播放器 AudioPlayer](/docs/components/audio-player/) - 音频播放控制组件
- [视频列表 VideoList](/docs/components/video-list/) - 视频列表展示组件
- [视频播放器 VideoPlayer](/docs/components/video-player/) - 视频播放控制组件
- [直播列表 LiveList](/docs/components/live-list/) - 直播内容列表组件
- [壁纸预览 WallpaperPreview](/docs/components/wallpaper-preview/) - 壁纸预览组件
- [视频背景 VideoBg](/docs/components/video-bg/) - 视频背景组件

### 功能组件

提供特定功能的组件。

- [浮动按钮 FloatingActionButtons](/docs/components/floating-action-buttons/) - 浮动操作按钮
- [视频弹层 VideoDialog](/docs/components/video-dialog/) - 视频弹窗组件
- [Feeds页面 FeedsPage](/docs/components/feeds-page/) - 信息流页面组件
- [礼物弹窗 GiftPopup](/docs/components/gift-popup/) - 礼物展示弹窗
- [KV组件 HelperKv](/docs/components/helper-kv/) - 键值对展示组件

## 🚀 快速开始
### 使用

```vue
<template>
  <div>
    <HelperRadio v-model="selected" name="demo" label="选项" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { HelperRadio } from 'share/helper-components'

const selected = ref(false)
</script>
```

## 📖 更多资源

- [快速开始](/docs/guide/getting-started) - 了解如何开始使用组件库
- [安装指南](/docs/guide/installation) - 详细的安装和配置说明
- [开发指南](/docs/guide/development) - 组件开发和贡献指南

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来帮助我们改进组件库。

---

> 如有问题或建议，请联系 WeGame 前端团队。
