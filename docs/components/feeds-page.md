# feeds-page 游戏动态页

::: tip 组件介绍
`feeds-page` 组件用于在页面中嵌入一个游戏动态（社区）内容模块。该组件通过 `iframe` 加载指定页面，并特别针对 WeGame 客户端环境进行了适配。在非 WeGame 环境下，组件将展示提示信息。它支持向内嵌页面注入自定义样式、切换主题，并提供了与内嵌页面通信的机制。
:::

## 页面模版

<demo src="feeds-page/example.vue"/>

## 基础用法

`feeds-page` 的基础用法需要提供 `appId`、`src` 和 `cssFileStyle` 三个核心属性。`src` 指定了 `iframe` 需要加载的页面地址，`cssFileStyle` 则用于向该页面注入自定义的 CSS 样式。

```vue
<template>
  <div class="feeds-demo-container">
    <FeedsPage
      appId="your-app-id"
      src="https://your-feeds-url.com"
      :cssFileStyle="customCss"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import FeedsPage from 'helper-components/feeds-page/index.vue';

// 用于注入 iframe 的自定义 CSS 字符串
const customCss = ref(`
  /* 隐藏内嵌页面的某个元素 */
  .unwanted-element { 
    display: none !important; 
  }
  /* 修改内嵌页面的背景色 */
  body { 
    background-color: #f5f5f5; 
  }
`);
</script>

<style scoped>
.feeds-demo-container {
  height: 600px;
  border: 1px solid #e0e0e0;
}
</style>
```

## 主题切换

通过 `colorMode` 属性，可以方便地控制内嵌页面的主题模式，支持 `light`（浅色）和 `dark`（深色）两种模式。组件会将该模式信息传递给内部的样式注入逻辑，以便应用不同的主题样式。

```vue
<template>
  <div>
    <div class="control-panel">
      <button @click="toggleTheme">
        切换为 {{ colorMode === 'light' ? '深色' : '浅色' }} 模式
      </button>
    </div>
    <div class="feeds-demo-container">
      <FeedsPage
        appId="your-app-id"
        src="https://your-feeds-url.com"
        :cssFileStyle="themeCss"
        :colorMode="colorMode"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import FeedsPage from 'helper-components/feeds-page/index.vue';

const colorMode = ref('light');
const themeCss = ref(`
  /* 在这里定义与主题相关的样式 */
  /* 例如，你可以使用 [data-colormode="dark"] 选择器 */
`);

const toggleTheme = () => {
  colorMode.value = colorMode.value === 'light' ? 'dark' : 'light';
};
</script>

<style scoped>
.control-panel {
  margin-bottom: 16px;
}
.feeds-demo-container {
  height: 600px;
  border: 1px solid #e0e0e0;
}
</style>
```

## 加载完成回调

组件在 `iframe` 加载并完成样式注入后，会触发 `complete` 事件。同时，你也可以通过 `callback` 属性传入一个回调函数，它会在样式注入后被执行。

```vue
<template>
  <div>
    <p>iFrame 加载状态: {{ loadStatus }}</p>
    <div class="feeds-demo-container">
      <FeedsPage
        appId="your-app-id"
        src="https://your-feeds-url.com"
        cssFileStyle=""
        :callback="onIframeLoad"
        @complete="onIframeComplete"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import FeedsPage from 'helper-components/feeds-page/index.vue';

const loadStatus = ref('加载中...');

// 通过 callback prop 接收回调
const onIframeLoad = () => {
  console.log('iFrame 已加载并注入样式 (通过 callback prop)');
};

// 通过 complete 事件接收回调
const onIframeComplete = () => {
  console.log('iFrame 初始化流程完成 (通过 complete event)');
  loadStatus.value = '加载完成';
};
</script>

<style scoped>
.feeds-demo-container {
  height: 600px;
  border: 1px solid #e0e0e0;
}
</style>
```

## 与内嵌页面通信

组件通过 `defineExpose` 暴露了 `postMessageToIFrame` 方法，允许父组件向 `iframe` 内的页面发送消息。你需要通过 `ref` 获取组件实例来调用此方法。

```vue
<template>
  <div>
    <div class="control-panel">
      <button @click="sendMessage">向 iFrame 发送消息</button>
    </div>
    <div class="feeds-demo-container">
      <FeedsPage
        ref="feedsPageRef"
        appId="your-app-id"
        src="https://your-feeds-url.com"
        cssFileStyle=""
        @complete="onReady"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import FeedsPage from 'helper-components/feeds-page/index.vue';

const feedsPageRef = ref(null);
const isReady = ref(false);

const onReady = () => {
  isReady.value = true;
  console.log('iFrame 准备就绪，可以发送消息。');
};

const sendMessage = () => {
  if (feedsPageRef.value && isReady.value) {
    const message = { type: 'user-action', payload: 'button-clicked' };
    feedsPageRef.value.postMessageToIFrame(message);
    alert('消息已发送，请在控制台查看');
    console.log('Message sent to iframe:', message);
  } else {
    alert('iFrame 尚未准备好，请稍后重试。');
  }
};
</script>

<style scoped>
.control-panel {
  margin-bottom: 16px;
}
.feeds-demo-container {
  height: 600px;
  border: 1px solid #e0e0e0;
}
</style>
```

## API

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
| :--- | :--- | :--- | :--- | :--- |
| `appId` | 应用的唯一标识符 | `string` | — | - |
| `src` | `iframe` 加载的页面 URL 地址 | `string` | — | - |
| `cssFileStyle` | 需要注入到 `iframe` 内部的 CSS 样式字符串 | `string` | — | - |
| `colorMode` | 颜色主题模式，用于控制内嵌页面的样式 | `'light'` \| `'dark'` | `light` / `dark` | `light` |
| `callback` | `iframe` 加载并注入样式后执行的回调函数 | `Function` | — | - |

### Events

| 事件名称 | 说明 | 回调参数 |
| :--- | :--- | :--- |
| `complete` | 当 `iframe` 加载完成且样式注入成功后触发 | - |

### Expose

| 方法名 | 说明 | 参数 |
| :--- | :--- | :--- |
| `postMessageToIFrame` | 向 `iframe` 内的页面发送消息 | `(params: T)` - `params` 是需要发送的数据对象 |

## 使用建议与注意事项

::: warning 注意事项
1. **环境依赖**：该组件强依赖 WeGame 客户端环境。在非 WeGame 环境下，它不会渲染 `iframe`，而是显示“游戏动态请在 WeGame 客户端内观看”的提示。
2. **样式注入**：`cssFileStyle` 属性用于定制 `iframe` 内部页面的外观，请确保提供合法有效的 CSS 代码。
3. **跨域策略**：`src` 属性指向的 URL 必须与主页面同源，或者目标页面设置了正确的 `Access-Control-Allow-Origin` 策略，以允许 `postMessage` 通信。
4. **通信协议**：使用 `postMessageToIFrame` 方法前，需要与内嵌页面的开发者约定好消息的数据结构和处理逻辑。
:::

::: tip 最佳实践
- **加载时机**：建议在 `complete` 事件触发后，再调用 `postMessageToIFrame` 方法，以确保 `iframe` 已完全准备好接收消息。
- **样式管理**：将不同主题（`light`/`dark`）的 CSS 规则写在同一个 `cssFileStyle` 字符串中，并使用 `[data-colormode="dark"]` 等属性选择器来区分，组件会自动处理 `colorMode` 的切换。
- **容错处理**：在调用 `postMessageToIFrame` 方法时，最好检查组件的 `ref` 是否存在，以避免在组件销毁或未挂载时出现错误。
:::