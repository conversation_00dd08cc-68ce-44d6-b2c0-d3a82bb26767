# HelperRadio 单选框

一个简洁美观的单选框组件，支持禁用状态和自定义样式，适用于表单和选择场景。

## 表单示例

<demo src="helper-radio/form-example.vue"/>

## 基础用法

最简单的用法，通过 `v-model` 绑定选中状态。

<demo src="helper-radio/basic.vue"/>

## 禁用状态

设置 `disabled` 属性禁用单选框。

<demo src="helper-radio/disabled.vue"/>

## 自定义 ID

通过 `id` 属性设置自定义 ID，便于表单关联和测试。

<demo src="helper-radio/custom-id.vue"/>

## API

### Props

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| modelValue | 绑定值，表示是否选中 | `boolean` | `false` | 否 |
| label | 单选框标签文本 | `string` | - | 是 |
| name | 原生 name 属性，用于分组 | `string` | - | 是 |
| id | 原生 id 属性 | `string` | `''` | 否 |
| disabled | 是否禁用 | `boolean` | `false` | 否 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 选中状态变化时触发 | `(checked: boolean) => void` |

## 注意事项

1. 同一组单选框需要使用相同的 `name` 属性
2. 需要手动处理单选逻辑，确保同组内只有一个选项被选中
3. `label` 属性是必填的，用于显示选项文本
4. 禁用状态下无法改变选中状态
5. 建议为每个单选框设置唯一的 `id` 属性

## 样式定制

组件使用 CSS 变量进行样式定制：

```css
.helper-radiobox {
  --ui-helper-radiobox-border: #3D596D; /* 边框颜色 */
  --ui-transition-opacity-1: opacity 0.25s ease; /* 过渡动画 */
}
```
