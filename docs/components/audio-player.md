# audio-player 音频播放器

::: tip 组件介绍
一个功能丰富的音频播放器组件，集成了播放列表、播放控制条和歌词展示功能，为用户提供完整的音频播放体验。
:::

## 页面模版

<demo src="audio-player/example.vue"/>

## 基础用法

通过 `audio-list` 属性传入一个音频对象数组来初始化播放器。组件会默认显示播放控制条和音频列表。

```vue
<template>
  <div>
    <AudioPlayer :audio-list="audioList" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import AudioPlayer from 'helper-components/audio-player/index.vue';

const audioList = ref([
  {
    url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
    title: 'SoundHelix Song 1',
    artist: 'SoundHelix',
    albumCover: 'https://via.placeholder.com/150/FFC107/000000?text=S1',
    duration: '03:41',
  },
  {
    url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3',
    title: 'SoundHelix Song 2',
    artist: 'SoundHelix',
    albumCover: 'https://via.placeholder.com/150/4CAF50/FFFFFF?text=S2',
    duration: '04:18',
  },
]);
</script>
```

## 显示歌词

在 `audio-list` 的音频对象中提供 `lrc` 字段（一个指向标准LRC歌词文件的URL），并设置 `show-lyric` 属性为 `true`，即可在播放器中展示歌词。

```vue
<template>
  <div>
    <AudioPlayer :audio-list="audioListWithLyrics" show-lyric />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import AudioPlayer from 'helper-components/audio-player/index.vue';

const audioListWithLyrics = ref([
  {
    url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-8.mp3',
    title: 'SoundHelix Song 8 (With Lyrics)',
    artist: 'SoundHelix',
    albumCover: 'https://via.placeholder.com/150/9C27B0/FFFFFF?text=S8',
    duration: '02:54',
    // 注意：这是一个示例URL，你需要提供一个可访问的真实LRC文件链接
    lrc: 'https://your-domain.com/path/to/your-lyrics.lrc',
  },
  {
    url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-9.mp3',
    title: 'SoundHelix Song 9 (No Lyrics)',
    artist: 'SoundHelix',
    albumCover: 'https://via.placeholder.com/150/3F51B5/FFFFFF?text=S9',
    duration: '05:23',
  },
]);
</script>
```

## 自定义列表行为

通过 `audio-index-visible` 和 `audio-into-view` 属性，可以自定义播放列表的行为。

- `audio-index-visible`: 控制是否在列表项标题前显示序号。
- `audio-into-view`: 控制当切换歌曲时，是否自动将当前播放的歌曲滚动到可视区域。

```vue
<template>
  <div>
    <AudioPlayer
      :audio-list="audioList"
      :audio-index-visible="false"
      :audio-into-view="false"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import AudioPlayer from 'helper-components/audio-player/index.vue';

const audioList = ref([
  {
    url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
    title: 'SoundHelix Song 1',
    artist: 'SoundHelix',
    albumCover: 'https://via.placeholder.com/150/FFC107/000000?text=S1',
    duration: '03:41',
  },
  {
    url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3',
    title: 'SoundHelix Song 2',
    artist: 'SoundHelix',
    albumCover: 'https://via.placeholder.com/150/4CAF50/FFFFFF?text=S2',
    duration: '04:18',
  },
  {
    url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3',
    title: 'SoundHelix Song 3',
    artist: 'SoundHelix',
    albumCover: 'https://via.placeholder.com/150/00BCD4/FFFFFF?text=S3',
    duration: '04:02',
  },
]);
</script>
```

## API

### AudioPlayer Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|:--------:|--------|
| audioList | 音频列表数据，是播放器的核心数据源。 | `AudioItem[]` | — | `[]` |
| showList | 是否显示播放列表 | `boolean` | — | `true` |
| showLyric | 是否显示歌词面板 | `boolean` | — | `false` |
| audioIndexVisible | 是否在播放列表的标题前显示序号 | `boolean` | — | `true` |
| audioIntoView | 切换歌曲时，是否将当前播放项滚动到可视区域 | `boolean` | — | `true` |
| appId | 用于数据上报的App ID | `string` | — | `''` |
| reportExt | 用于数据上报的扩展字段 | `string` | — | `''` |

### AudioItem Interface

`audioList` 属性接收一个对象数组，每个对象需符合 `AudioItem` 接口规范：

| 属性 | 说明 | 类型 | 是否必填 |
|------|------|------|:--------:|
| url | 音频文件的URL | `string` | 是 |
| title | 音频标题 | `string` | 是 |
| artist | 演唱者/艺术家 | `string` | 否 |
| albumCover | 专辑封面图片的URL | `string` | 否 |
| duration | 音频时长的显示文本，格式如 'mm:ss' | `string` | 否 |
| lrc | LRC歌词文件的URL | `string` | 否 |

### AudioPlayer Events

| 事件名称 | 说明 | 回调参数 |
|----------|------|----------|
| play | 当音频开始播放或从暂停状态恢复时触发 | `(audio: AudioItem)` |
| pause | 当音频暂停时触发 | `(audio: AudioItem)` |
| change | 当播放的音频切换时触发 | `(audio: AudioItem)` |
| ended | 当当前音频播放完成时触发 | `(audio: AudioItem)` |
| timeupdate | 当播放进度更新时触发，频率较高 | `(currentTime: number)` |
| error | 当音频加载或播放出错时触发 | `(error: ErrorEvent)` |


## 使用建议与注意事项

::: warning 注意事项
1. **数据源**：`audioList` 是组件的核心，请确保传入的数组中每个对象的 `url` 和 `title` 字段都存在且有效。
2. **歌词文件**：若要使用歌词功能，`lrc` 字段必须是一个可公开访问的URL，且文件内容需遵循标准的LRC格式。由于歌词文件需要通过网络请求获取，请处理好跨域（CORS）问题。
3. **时长显示**：`AudioItem` 中的 `duration` 字段仅用于在音频元数据加载完成前占位显示，组件会优先使用 `<audio>` 元素加载后的真实时长。
4. **错误处理**：当音频文件或歌词文件加载失败时，组件会在界面上显示错误提示。可以监听 `error` 事件进行自定义的错误处理逻辑。
:::

::: tip 最佳实践
- **提供完整元数据**：为了获得最佳的用户体验，建议为每个 `AudioItem` 提供完整的元数据，包括 `artist` 和 `albumCover`。
- **预加载**：对于重要的音频，可以考虑在应用的其他地方预加载音频文件，以减少用户的等待时间。
- **事件监听**：通过监听 `play`, `pause`, `change` 等事件，可以实现与应用其他部分的状态同步，例如更新页面标题或发送统计数据。
- **响应式布局**：该组件设计时已考虑响应式布局，但在嵌入复杂页面时，请确保其容器有合适的宽度，以保证良好的显示效果。
:::