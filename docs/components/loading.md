# HelperLoading 加载指示器

一个简洁美观的加载指示器组件，显示旋转动画和可选的文本内容，适用于数据加载和异步操作的反馈场景。

## 基础用法

最简单的用法，显示默认的旋转加载动画。

```vue
<template>
  <div>
    <HelperLoading />
  </div>
</template>

<script setup>
import HelperLoading from 'helper-components/loading/index.vue'
</script>
```

## 带文本的加载

通过插槽可以添加自定义的加载文本，提供更好的用户体验。

<demo src="loading/with-text.vue"/>

## 自定义样式

通过CSS变量可以自定义加载器的尺寸、颜色和动画速度。

<demo src="loading/custom-style.vue"/>

## API

### Props

该组件不接受任何props参数。

### Slots

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 加载文本内容 | - |

### Events

该组件不触发任何事件。

## 样式定制

组件使用 CSS 变量进行样式定制：

```css
.helper-loading {
  --helper-loading-width: 24px; /* 加载器宽度 */
  --helper-loading-height: 24px; /* 加载器高度 */
  --helper-loading-line-width: 4px; /* 边框宽度 */
  --helper-loading-line-color: rgba(0, 0, 0, 0.1); /* 背景圆环颜色 */
  --helper-loading-line-color-current: #4F6B87; /* 旋转部分颜色 */
  --helper-loading-line-color-text: #4F6B87; /* 文本颜色 */
  --helper-loading-line-duration: 1s; /* 动画持续时间 */
}
```

## 注意事项

1. 组件使用纯CSS动画实现，性能良好
2. 默认尺寸为24x24px，可通过CSS变量调整
3. 文本内容通过默认插槽传入，支持任意内容
4. 建议在异步操作期间显示，操作完成后隐藏
5. 可以通过父容器的`v-if`或`v-show`控制显示状态

## 使用场景

- 数据加载时的等待提示
- 表单提交时的处理状态
- 页面内容异步加载
- 文件上传进度提示
- API请求等待状态
